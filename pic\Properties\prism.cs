﻿using Nreal_ProductLine_Tool.PBS;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace image_test
{
    public partial class prism : Form
    {
        [UnmanagedFunctionPointerAttribute(CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        public delegate void ImgTransferCallback(IntPtr data, int count);
        int mesEnable = 0;
        public const int WM_CLOSE = 0x10;
        Form objform;
        Label portDetectLabel;
        string com_val;
        int band_rate;
        bool begin_or_reset = true;
        double previous_val = 0;
        bool isExited = false;
        string snRemote;  // 扫码程序传过来的sn码
        //string snPrism;
        string resMes; // 扫码程序传过来的mes资源
        bool isStopCapture = false;
        protected SemaphoreSlim message_sem;
        ImgTransferCallback imgTransfer = null;
        double[] para = new double[6];
        int trigger = 0;
        int show_flag = 1;
        int[] parameter = new int[12];
        int[] parameter1 = new int[12];
        Dictionary<string, string> mes_resource = new Dictionary<string, string>();

        public prism()
        {
            int ret = 0;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            this.FormClosed += FrmMain_FormClosed;

            /*
            ret = CameraImageLib.Init_spb(System.Environment.CurrentDirectory + "\\", 100, 20, 50, 10, 0, 0, 1);
            ret = CameraImageLib.AnalysisSPB(0, para);
            MessageBox.Show("ret " + ret);
            return;
            */

            this.TopLevel = false;
            this.FormBorderStyle = FormBorderStyle.None;
            this.Dock = DockStyle.Fill;

            System.Windows.Forms.Control.CheckForIllegalCrossThreadCalls = false;
            InitializeComponent();

            this.Text = LOGINFO.rname + " " + LOGINFO.pname + " Version:" + System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString() + " 线号：" + LOGINFO.lname;

            this.Load += new EventHandler(MainForm_Load);

            imgTransfer = imgTransferCallback;
            //para_init();
            para_init(ref parameter);
            ret = CameraImageLib.Init_prism2(parameter);
            //ret = CameraImageLib.Init_prism1(parameter);
            //ret = CameraImageLib.Init_spb(System.Environment.CurrentDirectory + "\\", 700, 250, 1200, 300, 300, 3350, 1000, 300, 300);
            if (ret != 0)
            {
                //MessageBox.Show("相机初始化失败 " + ret);
                //return;
            }

            //CameraImageLib.SetExposureTime(Configure.camera_exposure_time);
            //CameraImageLib.SetGain(Configure.camera_gain);
            //CameraImageLib.SetExposureTime(Configure.camera_exposure_time);
            //CameraImageLib.SetGain(Configure.camera_gain);

        }

        void para_init(ref int[] parameter)
        {
            parameter[0] = 2100;
            parameter[1] = 1650;
            parameter[2] = 800;
            parameter[3] = 200;
            parameter[4] = 850;
            parameter[5] = 400;
            parameter[6] = 200;
            parameter[7] = 400;
            parameter[8] = Configure.roi_bottom_x;
            parameter[9] = Configure.roi_bottom_y;
            parameter[10] = Configure.roi_bottom_width;
            parameter[11] = Configure.roi_bottom_height;
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // 设置窗体为全屏  
            this.WindowState = FormWindowState.Maximized;

            groupBox1.Anchor = (AnchorStyles.Top | AnchorStyles.Right);
            groupBox1.Location = new Point(this.ClientSize.Width - groupBox1.Width - 20, 20);

            //pictureBox1.Anchor = (AnchorStyles.Top | AnchorStyles.Left);
            pictureBox1.Location = new Point(20, 20);
            pictureBox1.Size = new Size(this.ClientSize.Width - groupBox1.Width - 60, this.ClientSize.Height - 60);
            //pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            // 初始设置（可根据需要调整）  
            //pictureBox1.SizeMode = PictureBoxSizeMode.Zoom; // 假设你想等比例放大图像  
            //groupBox1.Dock = DockStyle.None; // 确保GroupBox不是停靠的，以便我们可以手动调整其位置和尺寸 
            //pictureBox1.Image = System.Drawing.Image.FromFile("1.bmp");
            // 可选：隐藏任务栏（注意：这可能会影响用户体验）  
            // 调用 API 隐藏任务栏  
            // HideTaskbar();  
            //pictureBox1.Image = System.Drawing.Image.FromFile("1.bmp");
        }

        private void FrmMain_FormClosed(object sender, FormClosedEventArgs e)
        {
            Process.GetCurrentProcess().Kill();
        }

        public void imgTransferCallback(IntPtr data, int count)
        {
            byte[] managedArray = new byte[count];
            Marshal.Copy(data, managedArray, 0, count);
            MemoryStream stream = new MemoryStream(managedArray);

            //Logs.WriteInfo("imgTransferCallback", true);
            System.Drawing.Image bitMap = System.Drawing.Image.FromStream(stream);
            pictureBox1.BeginInvoke
            (
                new MethodInvoker
                (
                    delegate
                    {
                        {
                            if (pictureBox1.Image != null)
                            {
                                pictureBox1.Image.Dispose();
                            }
                            pictureBox1.Image = bitMap;
                        }
                    }
                )
            );
            bitMap.Save("pic.jpg");
            //double miliSeconds = ((TimeSpan)(DateTime.Now - begin)).TotalMilliseconds;
            //MessageBox.Show(miliSeconds.ToString());
        }

        private void button4_Click(object sender, EventArgs e)
        {
            //int ret = CameraImageLib.AnalysisPrism(1, para);
            CameraImageLib.RegisterCallBackShowImage(imgTransfer);
            parameter1 = new int[] { 90, 136, 796, 124, 60, 399, 60, 25 };
            int ret = CameraImageLib.AnalysisPrism2(1, parameter1, para);
            int[] para1 = new int[16];
            //int ret = CameraImageLib.AnalysisPrism1(1, para1);

            ret = 0;
            this.Invoke
            (
                new MethodInvoker
                (
                    delegate
                    {
                        {
                            if (ret != 0)
                            {
                                result_label.Text = "NG";
                                result_label.ForeColor = Color.Red;

                                label5.Text = "";
                                label6.Text = "";
                                label7.Text = "";
                                //label15.Text = "";
                                label9.Text = "";
                                //label17.Text = "";
                                label13.Text = "";
                            }
                            else
                            {
                                //result = 1;
                                //height = para[0];
                                //width = para[1];
                                //points = para[2];
                                result_label.ForeColor = Color.Green;
                                result_label.Text = "OK";

                                /*
                                if (ret == -1)
                                    result_label.Text = "未抓到上边沿两条边";
                                if (ret == -2)
                                    result_label.Text = "上边沿两边间隔过大";
                                if (ret == -3)
                                    result_label.Text = "上边沿两边间隔过小";
                                if (ret == -4)
                                    result_label.Text = "上边沿角度偏转超出范围";
                                if (ret == -5)
                                    result_label.Text = "左右边未抓全";
                                if (ret == -6)
                                    result_label.Text = "左边沿两边间隔过大";
                                if (ret == -7)
                                    result_label.Text = "左边沿两边间隔过小";
                                if (ret == -8)
                                    result_label.Text = "右边沿两边间隔过大";
                                if (ret == -9)
                                    result_label.Text = "右边沿两边间隔过小";
                                */
                                label5.Text = Math.Abs(para[0]).ToString();
                                label6.Text = Math.Abs(para[1]).ToString();
                                label7.Text = Math.Abs(para[2]).ToString();
                                //label15.Text = Math.Abs(para[3]).ToString();
                                label9.Text = Math.Abs(para[4]).ToString();
                                //label17.Text = Math.Abs(para[5]).ToString();
                                label13.Text = Math.Abs(para[2] - para[4]).ToString();
                                if (para[0] > Configure.upper_line_max_distance
                                    || para[0] < Configure.upper_line_min_distance)
                                {
                                    label5.BackColor = Color.Red;
                                }
                                else
                                    label5.BackColor = Color.Green;

                                if (para[1] > Configure.upper_line_angle
                                    || para[1] < -Configure.upper_line_angle)
                                {
                                    label6.BackColor = Color.Red;
                                }
                                else
                                    label6.BackColor = Color.Green;

                                if (para[2] > Configure.left_line_max_distance
                                    || para[2] < Configure.left_line_min_distance)
                                {
                                    label7.BackColor = Color.Red;
                                }
                                else
                                    label7.BackColor = Color.Green;

                                if (para[4] > Configure.right_line_max_distance
                                    || para[4] < Configure.right_line_min_distance)
                                {
                                    label9.BackColor = Color.Red;
                                }
                                else
                                    label9.BackColor = Color.Green;



                                if (Math.Abs(para[2] - para[4]) > Configure.right_line_max_distance)
                                {
                                    label13.BackColor = Color.Red;
                                }
                                else
                                    label13.BackColor = Color.Green;
                            }

                        }

                    }
                )
            );
        }
    }
}
