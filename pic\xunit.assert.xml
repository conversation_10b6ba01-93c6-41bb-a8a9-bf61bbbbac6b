<?xml version="1.0"?>
<doc>
    <assembly>
        <name>xunit.assert</name>
    </assembly>
    <members>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:Xunit.Assert">
            <summary>
            Contains various static methods that are used to verify that conditions are met during the
            process of running tests.
            </summary>
        </member>
        <member name="M:Xunit.Assert.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Assert"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Assert.Equals(System.Object,System.Object)">
            <summary>Do not call this method.</summary>
        </member>
        <member name="M:Xunit.Assert.ReferenceEquals(System.Object,System.Object)">
            <summary>Do not call this method.</summary>
        </member>
        <member name="M:Xunit.Assert.SafeGetGenericTypeDefinition(System.Type)">
            <summary>
            Safely perform <see cref="M:System.Type.GetGenericTypeDefinition"/>, returning <c>null</c> when the
            type is not generic.
            </summary>
            <param name="type">The potentially generic type</param>
            <returns>The generic type definition, when <paramref name="type"/> is generic; <c>null</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Assert.False(System.Boolean)">
            <summary>
            Verifies that the condition is false.
            </summary>
            <param name="condition">The condition to be tested</param>
            <exception cref="T:Xunit.Sdk.FalseException">Thrown if the condition is not false</exception>
        </member>
        <member name="M:Xunit.Assert.False(System.Nullable{System.Boolean})">
            <summary>
            Verifies that the condition is false.
            </summary>
            <param name="condition">The condition to be tested</param>
            <exception cref="T:Xunit.Sdk.FalseException">Thrown if the condition is not false</exception>
        </member>
        <member name="M:Xunit.Assert.False(System.Boolean,System.String)">
            <summary>
            Verifies that the condition is false.
            </summary>
            <param name="condition">The condition to be tested</param>
            <param name="userMessage">The message to show when the condition is not false</param>
            <exception cref="T:Xunit.Sdk.FalseException">Thrown if the condition is not false</exception>
        </member>
        <member name="M:Xunit.Assert.False(System.Nullable{System.Boolean},System.String)">
            <summary>
            Verifies that the condition is false.
            </summary>
            <param name="condition">The condition to be tested</param>
            <param name="userMessage">The message to show when the condition is not false</param>
            <exception cref="T:Xunit.Sdk.FalseException">Thrown if the condition is not false</exception>
        </member>
        <member name="M:Xunit.Assert.True(System.Boolean)">
            <summary>
            Verifies that an expression is true.
            </summary>
            <param name="condition">The condition to be inspected</param>
            <exception cref="T:Xunit.Sdk.TrueException">Thrown when the condition is false</exception>
        </member>
        <member name="M:Xunit.Assert.True(System.Nullable{System.Boolean})">
            <summary>
            Verifies that an expression is true.
            </summary>
            <param name="condition">The condition to be inspected</param>
            <exception cref="T:Xunit.Sdk.TrueException">Thrown when the condition is false</exception>
        </member>
        <member name="M:Xunit.Assert.True(System.Boolean,System.String)">
            <summary>
            Verifies that an expression is true.
            </summary>
            <param name="condition">The condition to be inspected</param>
            <param name="userMessage">The message to be shown when the condition is false</param>
            <exception cref="T:Xunit.Sdk.TrueException">Thrown when the condition is false</exception>
        </member>
        <member name="M:Xunit.Assert.True(System.Nullable{System.Boolean},System.String)">
            <summary>
            Verifies that an expression is true.
            </summary>
            <param name="condition">The condition to be inspected</param>
            <param name="userMessage">The message to be shown when the condition is false</param>
            <exception cref="T:Xunit.Sdk.TrueException">Thrown when the condition is false</exception>
        </member>
        <member name="M:Xunit.Assert.All``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
            <summary>
            Verifies that all items in the collection pass when executed against
            action.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="collection">The collection</param>
            <param name="action">The action to test each item against</param>
            <exception cref="T:Xunit.Sdk.AllException">Thrown when the collection contains at least one non-matching element</exception>
        </member>
        <member name="M:Xunit.Assert.All``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0,System.Int32})">
            <summary>
            Verifies that all items in the collection pass when executed against
            action. The item index is provided to the action, in addition to the item.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="collection">The collection</param>
            <param name="action">The action to test each item against</param>
            <exception cref="T:Xunit.Sdk.AllException">Thrown when the collection contains at least one non-matching element</exception>
        </member>
        <member name="M:Xunit.Assert.AllAsync``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Threading.Tasks.Task})">
            <summary>
            Verifies that all items in the collection pass when executed against
            action.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="collection">The collection</param>
            <param name="action">The action to test each item against</param>
            <exception cref="T:Xunit.Sdk.AllException">Thrown when the collection contains at least one non-matching element</exception>
        </member>
        <member name="M:Xunit.Assert.AllAsync``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Threading.Tasks.Task})">
            <summary>
            Verifies that all items in the collection pass when executed against
            action. The item index is provided to the action, in addition to the item.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="collection">The collection</param>
            <param name="action">The action to test each item against</param>
            <exception cref="T:Xunit.Sdk.AllException">Thrown when the collection contains at least one non-matching element</exception>
        </member>
        <member name="M:Xunit.Assert.Collection``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0}[])">
            <summary>
            Verifies that a collection contains exactly a given number of elements, which meet
            the criteria provided by the element inspectors.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="collection">The collection to be inspected</param>
            <param name="elementInspectors">The element inspectors, which inspect each element in turn. The
            total number of element inspectors must exactly match the number of elements in the collection.</param>
        </member>
        <member name="M:Xunit.Assert.CollectionAsync``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Threading.Tasks.Task}[])">
            <summary>
            Verifies that a collection contains exactly a given number of elements, which meet
            the criteria provided by the element inspectors.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="collection">The collection to be inspected</param>
            <param name="elementInspectors">The element inspectors, which inspect each element in turn. The
            total number of element inspectors must exactly match the number of elements in the collection.</param>
        </member>
        <member name="M:Xunit.Assert.Contains``1(``0,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Verifies that a collection contains a given object.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expected">The object expected to be in the collection</param>
            <param name="collection">The collection to be inspected</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.Contains``1(``0,System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Verifies that a collection contains a given object, using an equality comparer.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expected">The object expected to be in the collection</param>
            <param name="collection">The collection to be inspected</param>
            <param name="comparer">The comparer used to equate objects in the collection with the expected object</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.Contains``1(System.Collections.Generic.IEnumerable{``0},System.Predicate{``0})">
            <summary>
            Verifies that a collection contains a given object.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="collection">The collection to be inspected</param>
            <param name="filter">The filter used to find the item you're ensuring the collection contains</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.Distinct``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Verifies that a collection contains each object only once.
            </summary>
            <typeparam name="T">The type of the object to be compared</typeparam>
            <param name="collection">The collection to be inspected</param>
            <exception cref="T:Xunit.Sdk.DistinctException">Thrown when an object is present inside the collection more than once</exception>
        </member>
        <member name="M:Xunit.Assert.Distinct``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Verifies that a collection contains each object only once.
            </summary>
            <typeparam name="T">The type of the object to be compared</typeparam>
            <param name="collection">The collection to be inspected</param>
            <param name="comparer">The comparer used to equate objects in the collection with the expected object</param>
            <exception cref="T:Xunit.Sdk.DistinctException">Thrown when an object is present inside the collection more than once</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``1(``0,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Verifies that a collection does not contain a given object.
            </summary>
            <typeparam name="T">The type of the object to be compared</typeparam>
            <param name="expected">The object that is expected not to be in the collection</param>
            <param name="collection">The collection to be inspected</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the object is present inside the collection</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``1(``0,System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Verifies that a collection does not contain a given object, using an equality comparer.
            </summary>
            <typeparam name="T">The type of the object to be compared</typeparam>
            <param name="expected">The object that is expected not to be in the collection</param>
            <param name="collection">The collection to be inspected</param>
            <param name="comparer">The comparer used to equate objects in the collection with the expected object</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the object is present inside the collection</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``1(System.Collections.Generic.IEnumerable{``0},System.Predicate{``0})">
            <summary>
            Verifies that a collection does not contain a given object.
            </summary>
            <typeparam name="T">The type of the object to be compared</typeparam>
            <param name="collection">The collection to be inspected</param>
            <param name="filter">The filter used to find the item you're ensuring the collection does not contain</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the object is present inside the collection</exception>
        </member>
        <member name="M:Xunit.Assert.Empty(System.Collections.IEnumerable)">
            <summary>
            Verifies that a collection is empty.
            </summary>
            <param name="collection">The collection to be inspected</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the collection is null</exception>
            <exception cref="T:Xunit.Sdk.EmptyException">Thrown when the collection is not empty</exception>
        </member>
        <member name="M:Xunit.Assert.Equal``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Verifies that two sequences are equivalent, using a default comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <exception cref="T:Xunit.Sdk.EqualException">Thrown when the objects are not equal</exception>
        </member>
        <member name="M:Xunit.Assert.Equal``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Verifies that two sequences are equivalent, using a custom equatable comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="comparer">The comparer used to compare the two objects</param>
            <exception cref="T:Xunit.Sdk.EqualException">Thrown when the objects are not equal</exception>
        </member>
        <member name="M:Xunit.Assert.Equal``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Func{``0,``0,System.Boolean})">
            <summary>
            Verifies that two collections are equal, using a comparer function against
            items in the two collections.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="comparer">The function to compare two items for equality</param>
        </member>
        <member name="M:Xunit.Assert.NotEmpty(System.Collections.IEnumerable)">
            <summary>
            Verifies that a collection is not empty.
            </summary>
            <param name="collection">The collection to be inspected</param>
            <exception cref="T:System.ArgumentNullException">Thrown when a null collection is passed</exception>
            <exception cref="T:Xunit.Sdk.NotEmptyException">Thrown when the collection is empty</exception>
        </member>
        <member name="M:Xunit.Assert.NotEqual``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Verifies that two sequences are not equivalent, using a default comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected object</param>
            <param name="actual">The actual object</param>
            <exception cref="T:Xunit.Sdk.NotEqualException">Thrown when the objects are equal</exception>
        </member>
        <member name="M:Xunit.Assert.NotEqual``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Verifies that two sequences are not equivalent, using a custom equality comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected object</param>
            <param name="actual">The actual object</param>
            <param name="comparer">The comparer used to compare the two objects</param>
            <exception cref="T:Xunit.Sdk.NotEqualException">Thrown when the objects are equal</exception>
        </member>
        <member name="M:Xunit.Assert.NotEqual``1(System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEnumerable{``0},System.Func{``0,``0,System.Boolean})">
            <summary>
            Verifies that two collections are not equal, using a comparer function against
            items in the two collections.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="comparer">The function to compare two items for equality</param>
        </member>
        <member name="M:Xunit.Assert.Single(System.Collections.IEnumerable)">
            <summary>
            Verifies that the given collection contains only a single
            element of the given type.
            </summary>
            <param name="collection">The collection.</param>
            <returns>The single item in the collection.</returns>
            <exception cref="T:Xunit.Sdk.SingleException">Thrown when the collection does not contain
            exactly one element.</exception>
        </member>
        <member name="M:Xunit.Assert.Single(System.Collections.IEnumerable,System.Object)">
            <summary>
            Verifies that the given collection contains only a single
            element of the given value. The collection may or may not
            contain other values.
            </summary>
            <param name="collection">The collection.</param>
            <param name="expected">The value to find in the collection.</param>
            <returns>The single item in the collection.</returns>
            <exception cref="T:Xunit.Sdk.SingleException">Thrown when the collection does not contain
            exactly one element.</exception>
        </member>
        <member name="M:Xunit.Assert.Single``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Verifies that the given collection contains only a single
            element of the given type.
            </summary>
            <typeparam name="T">The collection type.</typeparam>
            <param name="collection">The collection.</param>
            <returns>The single item in the collection.</returns>
            <exception cref="T:Xunit.Sdk.SingleException">Thrown when the collection does not contain
            exactly one element.</exception>
        </member>
        <member name="M:Xunit.Assert.Single``1(System.Collections.Generic.IEnumerable{``0},System.Predicate{``0})">
            <summary>
            Verifies that the given collection contains only a single
            element of the given type which matches the given predicate. The
            collection may or may not contain other values which do not
            match the given predicate.
            </summary>
            <typeparam name="T">The collection type.</typeparam>
            <param name="collection">The collection.</param>
            <param name="predicate">The item matching predicate.</param>
            <returns>The single item in the filtered collection.</returns>
            <exception cref="T:Xunit.Sdk.SingleException">Thrown when the filtered collection does
            not contain exactly one element.</exception>
        </member>
        <member name="M:Xunit.Assert.Contains``2(``0,System.Collections.Generic.IDictionary{``0,``1})">
            <summary>
            Verifies that a dictionary contains a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <returns>The value associated with <paramref name="expected"/>.</returns>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.Contains``2(``0,System.Collections.Generic.IReadOnlyDictionary{``0,``1})">
            <summary>
            Verifies that a read-only dictionary contains a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <returns>The value associated with <paramref name="expected"/>.</returns>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.Contains``2(``0,System.Collections.Concurrent.ConcurrentDictionary{``0,``1})">
            <summary>
            Verifies that a dictionary contains a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <returns>The value associated with <paramref name="expected"/>.</returns>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.Contains``2(``0,System.Collections.Generic.Dictionary{``0,``1})">
            <summary>
            Verifies that a dictionary contains a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <returns>The value associated with <paramref name="expected"/>.</returns>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.Contains``2(``0,System.Collections.ObjectModel.ReadOnlyDictionary{``0,``1})">
            <summary>
            Verifies that a dictionary contains a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <returns>The value associated with <paramref name="expected"/>.</returns>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``2(``0,System.Collections.Generic.IDictionary{``0,``1})">
            <summary>
            Verifies that a dictionary does not contain a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the object is present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``2(``0,System.Collections.Generic.IReadOnlyDictionary{``0,``1})">
            <summary>
            Verifies that a dictionary does not contain a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the object is present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``2(``0,System.Collections.Concurrent.ConcurrentDictionary{``0,``1})">
            <summary>
            Verifies that a dictionary does not contain a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the object is present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``2(``0,System.Collections.Generic.Dictionary{``0,``1})">
            <summary>
            Verifies that a dictionary does not contain a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the object is present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``2(``0,System.Collections.ObjectModel.ReadOnlyDictionary{``0,``1})">
            <summary>
            Verifies that a dictionary does not contain a given key.
            </summary>
            <typeparam name="TKey">The type of the keys of the object to be verified.</typeparam>
            <typeparam name="TValue">The type of the values of the object to be verified.</typeparam>
            <param name="expected">The object expected to be in the collection.</param>
            <param name="collection">The collection to be inspected.</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the object is present in the collection</exception>
        </member>
        <member name="M:Xunit.Assert.Equal``1(``0,``0)">
            <summary>
            Verifies that two objects are equal, using a default comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
        </member>
        <member name="M:Xunit.Assert.Equal``1(``0,``0,System.Func{``0,``0,System.Boolean})">
            <summary>
            Verifies that two objects are equal, using a custom comparer function.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="comparer">The comparer used to compare the two objects</param>
        </member>
        <member name="M:Xunit.Assert.Equal``1(``0,``0,System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Verifies that two objects are equal, using a custom equatable comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="comparer">The comparer used to compare the two objects</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.Double,System.Double,System.Int32)">
            <summary>
            Verifies that two <see cref="T:System.Double"/> values are equal, within the number of decimal
            places given by <paramref name="precision"/>. The values are rounded before comparison.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-15)</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.Double,System.Double,System.Int32,System.MidpointRounding)">
            <summary>
            Verifies that two <see cref="T:System.Double"/> values are equal, within the number of decimal
            places given by <paramref name="precision"/>. The values are rounded before comparison.
            The rounding method to use is given by <paramref name="rounding" />
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-15)</param>
            <param name="rounding">Rounding method to use to process a number that is midway between two numbers</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.Double,System.Double,System.Double)">
            <summary>
            Verifies that two <see cref="T:System.Double"/> values are equal, within the tolerance given by
            <paramref name="tolerance"/> (positive or negative).
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="tolerance">The allowed difference between values</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.Single,System.Single,System.Int32)">
            <summary>
            Verifies that two <see cref="T:System.Single"/> values are equal, within the number of decimal
            places given by <paramref name="precision"/>. The values are rounded before comparison.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-15)</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.Single,System.Single,System.Int32,System.MidpointRounding)">
            <summary>
            Verifies that two <see cref="T:System.Single"/> values are equal, within the number of decimal
            places given by <paramref name="precision"/>. The values are rounded before comparison.
            The rounding method to use is given by <paramref name="rounding" />
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-15)</param>
            <param name="rounding">Rounding method to use to process a number that is midway between two numbers</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.Single,System.Single,System.Single)">
            <summary>
            Verifies that two <see cref="T:System.Single"/> values are equal, within the tolerance given by
            <paramref name="tolerance"/> (positive or negative).
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="tolerance">The allowed difference between values</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.Decimal,System.Decimal,System.Int32)">
            <summary>
            Verifies that two <see cref="T:System.Decimal"/> values are equal, within the number of decimal
            places given by <paramref name="precision"/>. The values are rounded before comparison.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-28)</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.DateTime,System.DateTime)">
            <summary>
            Verifies that two <see cref="T:System.DateTime"/> values are equal.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.DateTime,System.DateTime,System.TimeSpan)">
            <summary>
            Verifies that two <see cref="T:System.DateTime"/> values are equal, within the precision
            given by <paramref name="precision"/>.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The allowed difference in time where the two dates are considered equal</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.DateTimeOffset,System.DateTimeOffset)">
            <summary>
            Verifies that two <see cref="T:System.DateTimeOffset"/> values are equal.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
        </member>
        <member name="M:Xunit.Assert.Equal(System.DateTimeOffset,System.DateTimeOffset,System.TimeSpan)">
            <summary>
            Verifies that two <see cref="T:System.DateTimeOffset"/> values are equal, within the precision
            given by <paramref name="precision"/>.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The allowed difference in time where the two dates are considered equal</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual``1(``0,``0)">
            <summary>
            Verifies that two objects are not equal, using a default comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected object</param>
            <param name="actual">The actual object</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual``1(``0,``0,System.Func{``0,``0,System.Boolean})">
            <summary>
            Verifies that two objects are not equal, using a custom equality comparer function.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected object</param>
            <param name="actual">The actual object</param>
            <param name="comparer">The comparer used to examine the objects</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual``1(``0,``0,System.Collections.Generic.IEqualityComparer{``0})">
            <summary>
            Verifies that two objects are not equal, using a custom equality comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected object</param>
            <param name="actual">The actual object</param>
            <param name="comparer">The comparer used to examine the objects</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual(System.Double,System.Double,System.Int32)">
            <summary>
            Verifies that two <see cref="T:System.Double"/> values are not equal, within the number of decimal
            places given by <paramref name="precision"/>.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-15)</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual(System.Double,System.Double,System.Int32,System.MidpointRounding)">
            <summary>
            Verifies that two <see cref="T:System.Double"/> values are not equal, within the number of decimal
            places given by <paramref name="precision"/>. The values are rounded before comparison.
            The rounding method to use is given by <paramref name="rounding" />
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-15)</param>
            <param name="rounding">Rounding method to use to process a number that is midway between two numbers</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual(System.Double,System.Double,System.Double)">
            <summary>
            Verifies that two <see cref="T:System.Double"/> values are not equal, within the tolerance given by
            <paramref name="tolerance"/> (positive or negative).
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="tolerance">The allowed difference between values</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual(System.Single,System.Single,System.Int32)">
            <summary>
            Verifies that two <see cref="T:System.Single"/> values are not equal, within the number of decimal
            places given by <paramref name="precision"/>.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-15)</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual(System.Single,System.Single,System.Int32,System.MidpointRounding)">
            <summary>
            Verifies that two <see cref="T:System.Single"/> values are not equal, within the number of decimal
            places given by <paramref name="precision"/>. The values are rounded before comparison.
            The rounding method to use is given by <paramref name="rounding" />
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-15)</param>
            <param name="rounding">Rounding method to use to process a number that is midway between two numbers</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual(System.Single,System.Single,System.Single)">
            <summary>
            Verifies that two <see cref="T:System.Single"/> values are not equal, within the tolerance given by
            <paramref name="tolerance"/> (positive or negative).
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="tolerance">The allowed difference between values</param>
        </member>
        <member name="M:Xunit.Assert.NotEqual(System.Decimal,System.Decimal,System.Int32)">
            <summary>
            Verifies that two <see cref="T:System.Decimal"/> values are not equal, within the number of decimal
            places given by <paramref name="precision"/>.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
            <param name="precision">The number of decimal places (valid values: 0-28)</param>
        </member>
        <member name="M:Xunit.Assert.NotStrictEqual``1(``0,``0)">
            <summary>
            Verifies that two objects are strictly not equal, using the type's default comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected object</param>
            <param name="actual">The actual object</param>
        </member>
        <member name="M:Xunit.Assert.StrictEqual``1(``0,``0)">
            <summary>
            Verifies that two objects are strictly equal, using the type's default comparer.
            </summary>
            <typeparam name="T">The type of the objects to be compared</typeparam>
            <param name="expected">The expected value</param>
            <param name="actual">The value to be compared against</param>
        </member>
        <member name="M:Xunit.Assert.Equivalent(System.Object,System.Object,System.Boolean)">
            <summary>
            Verifies that two objects are equivalent, using a default comparer. This comparison is done
            without regard to type, and only inspects public property and field values for individual
            equality. Deep equivalence tests (meaning, property or fields which are themselves complex
            types) are supported. With strict mode off, object comparison allows <paramref name="actual"/>
            to have extra public members that aren't part of <paramref name="expected"/>, and collection
            comparison allows <paramref name="actual"/> to have more data in it than is present in
            <paramref name="expected"/>; with strict mode on, those rules are tightened to require exact
            member list (for objects) or data (for collections).
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
            <param name="strict">A flag which enables strict comparison mode</param>
        </member>
        <member name="M:Xunit.Assert.Raises``1(System.Action{System.EventHandler{``0}},System.Action{System.EventHandler{``0}},System.Action)">
            <summary>
            Verifies that an event with the exact event args is raised.
            </summary>
            <typeparam name="T">The type of the event arguments to expect</typeparam>
            <param name="attach">Code to attach the event handler</param>
            <param name="detach">Code to detach the event handler</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The event sender and arguments wrapped in an object</returns>
            <exception cref="T:Xunit.Sdk.RaisesException">Thrown when the expected event was not raised.</exception>
        </member>
        <member name="M:Xunit.Assert.RaisesAny(System.Action{System.EventHandler},System.Action{System.EventHandler},System.Action)">
            <summary>
            Verifies that an event is raised.
            </summary>
            <param name="attach">Code to attach the event handler</param>
            <param name="detach">Code to detach the event handler</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The event sender and arguments wrapped in an object</returns>
            <exception cref="T:Xunit.Sdk.RaisesException">Thrown when the expected event was not raised.</exception>
        </member>
        <member name="M:Xunit.Assert.RaisesAny``1(System.Action{System.EventHandler{``0}},System.Action{System.EventHandler{``0}},System.Action)">
            <summary>
            Verifies that an event with the exact or a derived event args is raised.
            </summary>
            <typeparam name="T">The type of the event arguments to expect</typeparam>
            <param name="attach">Code to attach the event handler</param>
            <param name="detach">Code to detach the event handler</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The event sender and arguments wrapped in an object</returns>
            <exception cref="T:Xunit.Sdk.RaisesException">Thrown when the expected event was not raised.</exception>
        </member>
        <member name="M:Xunit.Assert.RaisesAnyAsync(System.Action{System.EventHandler},System.Action{System.EventHandler},System.Func{System.Threading.Tasks.Task})">
            <summary>
            Verifies that an event is raised.
            </summary>
            <param name="attach">Code to attach the event handler</param>
            <param name="detach">Code to detach the event handler</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The event sender and arguments wrapped in an object</returns>
            <exception cref="T:Xunit.Sdk.RaisesException">Thrown when the expected event was not raised.</exception>
        </member>
        <member name="M:Xunit.Assert.RaisesAnyAsync``1(System.Action{System.EventHandler{``0}},System.Action{System.EventHandler{``0}},System.Func{System.Threading.Tasks.Task})">
            <summary>
            Verifies that an event with the exact or a derived event args is raised.
            </summary>
            <typeparam name="T">The type of the event arguments to expect</typeparam>
            <param name="attach">Code to attach the event handler</param>
            <param name="detach">Code to detach the event handler</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The event sender and arguments wrapped in an object</returns>
            <exception cref="T:Xunit.Sdk.RaisesException">Thrown when the expected event was not raised.</exception>
        </member>
        <member name="M:Xunit.Assert.RaisesAsync``1(System.Action{System.EventHandler{``0}},System.Action{System.EventHandler{``0}},System.Func{System.Threading.Tasks.Task})">
            <summary>
            Verifies that an event with the exact event args (and not a derived type) is raised.
            </summary>
            <typeparam name="T">The type of the event arguments to expect</typeparam>
            <param name="attach">Code to attach the event handler</param>
            <param name="detach">Code to detach the event handler</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The event sender and arguments wrapped in an object</returns>
            <exception cref="T:Xunit.Sdk.RaisesException">Thrown when the expected event was not raised.</exception>
        </member>
        <member name="T:Xunit.Assert.RaisedEvent`1">
            <summary>
            Represents a raised event after the fact.
            </summary>
            <typeparam name="T">The type of the event arguments.</typeparam>
        </member>
        <member name="P:Xunit.Assert.RaisedEvent`1.Sender">
            <summary>
            The sender of the event.
            </summary>
        </member>
        <member name="P:Xunit.Assert.RaisedEvent`1.Arguments">
            <summary>
            The event arguments.
            </summary>
        </member>
        <member name="M:Xunit.Assert.RaisedEvent`1.#ctor(System.Object,`0)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Assert.RaisedEvent`1" /> class.
            </summary>
            <param name="sender">The sender of the event.</param>
            <param name="args">The event arguments</param>
        </member>
        <member name="M:Xunit.Assert.Throws(System.Type,System.Action)">
            <summary>
            Verifies that the exact exception is thrown (and not a derived exception type).
            </summary>
            <param name="exceptionType">The type of the exception expected to be thrown</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.Throws(System.Type,System.Func{System.Object})">
            <summary>
            Verifies that the exact exception is thrown (and not a derived exception type).
            Generally used to test property accessors.
            </summary>
            <param name="exceptionType">The type of the exception expected to be thrown</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.Throws(System.Type,System.Func{System.Threading.Tasks.Task})">
            <summary/>
        </member>
        <member name="M:Xunit.Assert.Throws``1(System.Action)">
            <summary>
            Verifies that the exact exception is thrown (and not a derived exception type).
            </summary>
            <typeparam name="T">The type of the exception expected to be thrown</typeparam>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.Throws``1(System.Func{System.Object})">
            <summary>
            Verifies that the exact exception is thrown (and not a derived exception type).
            Generally used to test property accessors.
            </summary>
            <typeparam name="T">The type of the exception expected to be thrown</typeparam>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.Throws``1(System.Func{System.Threading.Tasks.Task})">
            <summary/>
        </member>
        <member name="M:Xunit.Assert.Throws``1(System.String,System.Action)">
            <summary>
            Verifies that the exact exception is thrown (and not a derived exception type), where the exception
            derives from <see cref="T:System.ArgumentException"/> and has the given parameter name.
            </summary>
            <param name="paramName">The parameter name that is expected to be in the exception</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.Throws``1(System.String,System.Func{System.Object})">
            <summary>
            Verifies that the exact exception is thrown (and not a derived exception type), where the exception
            derives from <see cref="T:System.ArgumentException"/> and has the given parameter name.
            </summary>
            <param name="paramName">The parameter name that is expected to be in the exception</param>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.Throws``1(System.String,System.Func{System.Threading.Tasks.Task})">
            <summary/>
        </member>
        <member name="M:Xunit.Assert.ThrowsAny``1(System.Action)">
            <summary>
            Verifies that the exact exception or a derived exception type is thrown.
            </summary>
            <typeparam name="T">The type of the exception expected to be thrown</typeparam>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.ThrowsAny``1(System.Func{System.Object})">
            <summary>
            Verifies that the exact exception or a derived exception type is thrown.
            Generally used to test property accessors.
            </summary>
            <typeparam name="T">The type of the exception expected to be thrown</typeparam>
            <param name="testCode">A delegate to the code to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.ThrowsAny``1(System.Func{System.Threading.Tasks.Task})">
            <summary/>
        </member>
        <member name="M:Xunit.Assert.ThrowsAnyAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Verifies that the exact exception or a derived exception type is thrown.
            </summary>
            <typeparam name="T">The type of the exception expected to be thrown</typeparam>
            <param name="testCode">A delegate to the task to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.ThrowsAsync(System.Type,System.Func{System.Threading.Tasks.Task})">
            <summary>
            Verifies that the exact exception is thrown (and not a derived exception type).
            </summary>
            <param name="exceptionType">The type of the exception expected to be thrown</param>
            <param name="testCode">A delegate to the task to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.ThrowsAsync``1(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Verifies that the exact exception is thrown (and not a derived exception type).
            </summary>
            <typeparam name="T">The type of the exception expected to be thrown</typeparam>
            <param name="testCode">A delegate to the task to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.ThrowsAsync``1(System.String,System.Func{System.Threading.Tasks.Task})">
            <summary>
            Verifies that the exact exception is thrown (and not a derived exception type), where the exception
            derives from <see cref="T:System.ArgumentException"/> and has the given parameter name.
            </summary>
            <param name="paramName">The parameter name that is expected to be in the exception</param>
            <param name="testCode">A delegate to the task to be tested</param>
            <returns>The exception that was thrown, when successful</returns>
        </member>
        <member name="M:Xunit.Assert.Fail(System.String)">
            <summary>
            Indicates that the test should immediately fail.
            </summary>
            <param name="message">The optional failure message</param>
        </member>
        <member name="M:Xunit.Assert.GuardArgumentNotNull``1(System.String,``0)">
            <summary/>
        </member>
        <member name="M:Xunit.Assert.NotSame(System.Object,System.Object)">
            <summary>
            Verifies that two objects are not the same instance.
            </summary>
            <param name="expected">The expected object instance</param>
            <param name="actual">The actual object instance</param>
            <exception cref="T:Xunit.Sdk.NotSameException">Thrown when the objects are the same instance</exception>
        </member>
        <member name="M:Xunit.Assert.Same(System.Object,System.Object)">
            <summary>
            Verifies that two objects are the same instance.
            </summary>
            <param name="expected">The expected object instance</param>
            <param name="actual">The actual object instance</param>
            <exception cref="T:Xunit.Sdk.SameException">Thrown when the objects are not the same instance</exception>
        </member>
        <member name="M:Xunit.Assert.Multiple(System.Action[])">
            <summary>
            Runs multiple checks, collecting the exceptions from each one, and then bundles all failures
            up into a single assertion failure.
            </summary>
            <param name="checks">The individual assertions to run, as actions.</param>
        </member>
        <member name="M:Xunit.Assert.NotNull(System.Object)">
            <summary>
            Verifies that an object reference is not null.
            </summary>
            <param name="object">The object to be validated</param>
            <exception cref="T:Xunit.Sdk.NotNullException">Thrown when the object reference is null</exception>
        </member>
        <member name="M:Xunit.Assert.NotNull``1(System.Nullable{``0})">
            <summary>
            Verifies that a nullable struct value is not null.
            </summary>
            <typeparam name="T">The type of the struct</typeparam>
            <param name="value">The value to e validated</param>
            <returns>The non-<c>null</c> value</returns>
            <exception cref="T:Xunit.Sdk.NotNullException">Thrown when the value is null</exception>
        </member>
        <member name="M:Xunit.Assert.Null(System.Object)">
            <summary>
            Verifies that an object reference is null.
            </summary>
            <param name="object">The object to be inspected</param>
            <exception cref="T:Xunit.Sdk.NullException">Thrown when the object reference is not null</exception>
        </member>
        <member name="M:Xunit.Assert.Null``1(System.Nullable{``0})">
            <summary>
            Verifies that a nullable struct value is null.
            </summary>
            <param name="value">The value to be inspected</param>
            <exception cref="T:Xunit.Sdk.NullException">Thrown when the value is not null</exception>
        </member>
        <member name="M:Xunit.Assert.PropertyChanged(System.ComponentModel.INotifyPropertyChanged,System.String,System.Action)">
            <summary>
            Verifies that the provided object raised <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/>
            as a result of executing the given test code.
            </summary>
            <param name="object">The object which should raise the notification</param>
            <param name="propertyName">The property name for which the notification should be raised</param>
            <param name="testCode">The test code which should cause the notification to be raised</param>
            <exception cref="T:Xunit.Sdk.PropertyChangedException">Thrown when the notification is not raised</exception>
        </member>
        <member name="M:Xunit.Assert.PropertyChanged(System.ComponentModel.INotifyPropertyChanged,System.String,System.Func{System.Threading.Tasks.Task})">
            <summary/>
        </member>
        <member name="M:Xunit.Assert.PropertyChangedAsync(System.ComponentModel.INotifyPropertyChanged,System.String,System.Func{System.Threading.Tasks.Task})">
            <summary>
            Verifies that the provided object raised <see cref="E:System.ComponentModel.INotifyPropertyChanged.PropertyChanged"/>
            as a result of executing the given test code.
            </summary>
            <param name="object">The object which should raise the notification</param>
            <param name="propertyName">The property name for which the notification should be raised</param>
            <param name="testCode">The test code which should cause the notification to be raised</param>
            <exception cref="T:Xunit.Sdk.PropertyChangedException">Thrown when the notification is not raised</exception>
        </member>
        <member name="M:Xunit.Assert.InRange``1(``0,``0,``0)">
            <summary>
            Verifies that a value is within a given range.
            </summary>
            <typeparam name="T">The type of the value to be compared</typeparam>
            <param name="actual">The actual value to be evaluated</param>
            <param name="low">The (inclusive) low value of the range</param>
            <param name="high">The (inclusive) high value of the range</param>
            <exception cref="T:Xunit.Sdk.InRangeException">Thrown when the value is not in the given range</exception>
        </member>
        <member name="M:Xunit.Assert.InRange``1(``0,``0,``0,System.Collections.Generic.IComparer{``0})">
            <summary>
            Verifies that a value is within a given range, using a comparer.
            </summary>
            <typeparam name="T">The type of the value to be compared</typeparam>
            <param name="actual">The actual value to be evaluated</param>
            <param name="low">The (inclusive) low value of the range</param>
            <param name="high">The (inclusive) high value of the range</param>
            <param name="comparer">The comparer used to evaluate the value's range</param>
            <exception cref="T:Xunit.Sdk.InRangeException">Thrown when the value is not in the given range</exception>
        </member>
        <member name="M:Xunit.Assert.NotInRange``1(``0,``0,``0)">
            <summary>
            Verifies that a value is not within a given range, using the default comparer.
            </summary>
            <typeparam name="T">The type of the value to be compared</typeparam>
            <param name="actual">The actual value to be evaluated</param>
            <param name="low">The (inclusive) low value of the range</param>
            <param name="high">The (inclusive) high value of the range</param>
            <exception cref="T:Xunit.Sdk.NotInRangeException">Thrown when the value is in the given range</exception>
        </member>
        <member name="M:Xunit.Assert.NotInRange``1(``0,``0,``0,System.Collections.Generic.IComparer{``0})">
            <summary>
            Verifies that a value is not within a given range, using a comparer.
            </summary>
            <typeparam name="T">The type of the value to be compared</typeparam>
            <param name="actual">The actual value to be evaluated</param>
            <param name="low">The (inclusive) low value of the range</param>
            <param name="high">The (inclusive) high value of the range</param>
            <param name="comparer">The comparer used to evaluate the value's range</param>
            <exception cref="T:Xunit.Sdk.NotInRangeException">Thrown when the value is in the given range</exception>
        </member>
        <member name="M:Xunit.Assert.RecordException(System.Action)">
            <summary>
            Records any exception which is thrown by the given code.
            </summary>
            <param name="testCode">The code which may thrown an exception.</param>
            <returns>Returns the exception that was thrown by the code; null, otherwise.</returns>
        </member>
        <member name="M:Xunit.Assert.RecordException(System.Func{System.Object},System.String)">
            <summary>
            Records any exception which is thrown by the given code that has
            a return value. Generally used for testing property accessors.
            </summary>
            <param name="testCode">The code which may thrown an exception.</param>
            <param name="asyncMethodName">The name of the async method the user should've called if they accidentally
            passed in an async function</param>
            <returns>Returns the exception that was thrown by the code; null, otherwise.</returns>
        </member>
        <member name="M:Xunit.Assert.RecordException(System.Func{System.Threading.Tasks.Task})">
            <summary/>
        </member>
        <member name="M:Xunit.Assert.RecordExceptionAsync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Records any exception which is thrown by the given task.
            </summary>
            <param name="testCode">The task which may thrown an exception.</param>
            <returns>Returns the exception that was thrown by the code; null, otherwise.</returns>
        </member>
        <member name="M:Xunit.Assert.Contains``1(``0,System.Collections.Generic.ISet{``0})">
            <summary>
            Verifies that the set contains the given object.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expected">The object expected to be in the set</param>
            <param name="set">The set to be inspected</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the set</exception>
        </member>
        <member name="M:Xunit.Assert.Contains``1(``0,System.Collections.Generic.HashSet{``0})">
            <summary>
            Verifies that the hashset contains the given object.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expected">The object expected to be in the set</param>
            <param name="set">The set to be inspected</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the set</exception>
        </member>
        <member name="M:Xunit.Assert.Contains``1(``0,System.Collections.Generic.SortedSet{``0})">
            <summary>
            Verifies that the sorted hashset contains the given object.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expected">The object expected to be in the set</param>
            <param name="set">The set to be inspected</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the set</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``1(``0,System.Collections.Generic.ISet{``0})">
            <summary>
            Verifies that the set does not contain the given item.
            </summary>
            <typeparam name="T">The type of the object to be compared</typeparam>
            <param name="expected">The object that is expected not to be in the set</param>
            <param name="set">The set to be inspected</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the object is present inside the set</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``1(``0,System.Collections.Generic.HashSet{``0})">
            <summary>
            Verifies that the hashset does not contain the given item.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expected">The object expected to be in the set</param>
            <param name="set">The set to be inspected</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the set</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain``1(``0,System.Collections.Generic.SortedSet{``0})">
            <summary>
            Verifies that the sorted hashset does not contain the given item.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expected">The object expected to be in the set</param>
            <param name="set">The set to be inspected</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the object is not present in the set</exception>
        </member>
        <member name="M:Xunit.Assert.ProperSubset``1(System.Collections.Generic.ISet{``0},System.Collections.Generic.ISet{``0})">
            <summary>
            Verifies that a set is a proper subset of another set.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expectedSubset">The expected subset</param>
            <param name="actual">The set expected to be a proper subset</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the actual set is not a proper subset of the expected set</exception>
        </member>
        <member name="M:Xunit.Assert.ProperSuperset``1(System.Collections.Generic.ISet{``0},System.Collections.Generic.ISet{``0})">
            <summary>
            Verifies that a set is a proper superset of another set.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expectedSuperset">The expected superset</param>
            <param name="actual">The set expected to be a proper superset</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the actual set is not a proper superset of the expected set</exception>
        </member>
        <member name="M:Xunit.Assert.Subset``1(System.Collections.Generic.ISet{``0},System.Collections.Generic.ISet{``0})">
            <summary>
            Verifies that a set is a subset of another set.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expectedSubset">The expected subset</param>
            <param name="actual">The set expected to be a subset</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the actual set is not a subset of the expected set</exception>
        </member>
        <member name="M:Xunit.Assert.Superset``1(System.Collections.Generic.ISet{``0},System.Collections.Generic.ISet{``0})">
            <summary>
            Verifies that a set is a superset of another set.
            </summary>
            <typeparam name="T">The type of the object to be verified</typeparam>
            <param name="expectedSuperset">The expected superset</param>
            <param name="actual">The set expected to be a superset</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the actual set is not a superset of the expected set</exception>
        </member>
        <member name="M:Xunit.Assert.Contains(System.String,System.String)">
            <summary>
            Verifies that a string contains a given sub-string, using the current culture.
            </summary>
            <param name="expectedSubstring">The sub-string expected to be in the string</param>
            <param name="actualString">The string to be inspected</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the sub-string is not present inside the string</exception>
        </member>
        <member name="M:Xunit.Assert.Contains(System.String,System.String,System.StringComparison)">
            <summary>
            Verifies that a string contains a given sub-string, using the given comparison type.
            </summary>
            <param name="expectedSubstring">The sub-string expected to be in the string</param>
            <param name="actualString">The string to be inspected</param>
            <param name="comparisonType">The type of string comparison to perform</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the sub-string is not present inside the string</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain(System.String,System.String)">
            <summary>
            Verifies that a string does not contain a given sub-string, using the current culture.
            </summary>
            <param name="expectedSubstring">The sub-string which is expected not to be in the string</param>
            <param name="actualString">The string to be inspected</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the sub-string is present inside the string</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotContain(System.String,System.String,System.StringComparison)">
            <summary>
            Verifies that a string does not contain a given sub-string, using the current culture.
            </summary>
            <param name="expectedSubstring">The sub-string which is expected not to be in the string</param>
            <param name="actualString">The string to be inspected</param>
            <param name="comparisonType">The type of string comparison to perform</param>
            <exception cref="T:Xunit.Sdk.DoesNotContainException">Thrown when the sub-string is present inside the given string</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotMatch(System.String,System.String)">
            <summary>
            Verifies that a string does not match a regular expression.
            </summary>
            <param name="expectedRegexPattern">The regex pattern expected not to match</param>
            <param name="actualString">The string to be inspected</param>
            <exception cref="T:Xunit.Sdk.DoesNotMatchException">Thrown when the string matches the regex pattern</exception>
        </member>
        <member name="M:Xunit.Assert.DoesNotMatch(System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Verifies that a string does not match a regular expression.
            </summary>
            <param name="expectedRegex">The regex expected not to match</param>
            <param name="actualString">The string to be inspected</param>
            <exception cref="T:Xunit.Sdk.DoesNotMatchException">Thrown when the string matches the regex</exception>
        </member>
        <member name="M:Xunit.Assert.Empty(System.String)">
            <summary>
            Verifies that a string is empty.
            </summary>
            <param name="value">The string value to be inspected</param>
            <exception cref="T:System.ArgumentNullException">Thrown when the string is null</exception>
            <exception cref="T:Xunit.Sdk.EmptyException">Thrown when the string is not empty</exception>
        </member>
        <member name="M:Xunit.Assert.EndsWith(System.String,System.String)">
            <summary>
            Verifies that a string ends with a given string, using the current culture.
            </summary>
            <param name="expectedEndString">The string expected to be at the end of the string</param>
            <param name="actualString">The string to be inspected</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the string does not end with the expected string</exception>
        </member>
        <member name="M:Xunit.Assert.EndsWith(System.String,System.String,System.StringComparison)">
            <summary>
            Verifies that a string ends with a given string, using the given comparison type.
            </summary>
            <param name="expectedEndString">The string expected to be at the end of the string</param>
            <param name="actualString">The string to be inspected</param>
            <param name="comparisonType">The type of string comparison to perform</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the string does not end with the expected string</exception>
        </member>
        <member name="M:Xunit.Assert.StartsWith(System.String,System.String)">
            <summary>
            Verifies that a string starts with a given string, using the current culture.
            </summary>
            <param name="expectedStartString">The string expected to be at the start of the string</param>
            <param name="actualString">The string to be inspected</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the string does not start with the expected string</exception>
        </member>
        <member name="M:Xunit.Assert.StartsWith(System.String,System.String,System.StringComparison)">
            <summary>
            Verifies that a string starts with a given string, using the given comparison type.
            </summary>
            <param name="expectedStartString">The string expected to be at the start of the string</param>
            <param name="actualString">The string to be inspected</param>
            <param name="comparisonType">The type of string comparison to perform</param>
            <exception cref="T:Xunit.Sdk.ContainsException">Thrown when the string does not start with the expected string</exception>
        </member>
        <member name="M:Xunit.Assert.Matches(System.String,System.String)">
            <summary>
            Verifies that a string matches a regular expression.
            </summary>
            <param name="expectedRegexPattern">The regex pattern expected to match</param>
            <param name="actualString">The string to be inspected</param>
            <exception cref="T:Xunit.Sdk.MatchesException">Thrown when the string does not match the regex pattern</exception>
        </member>
        <member name="M:Xunit.Assert.Matches(System.Text.RegularExpressions.Regex,System.String)">
            <summary>
            Verifies that a string matches a regular expression.
            </summary>
            <param name="expectedRegex">The regex expected to match</param>
            <param name="actualString">The string to be inspected</param>
            <exception cref="T:Xunit.Sdk.MatchesException">Thrown when the string does not match the regex</exception>
        </member>
        <member name="M:Xunit.Assert.Equal(System.String,System.String)">
            <summary>
            Verifies that two strings are equivalent.
            </summary>
            <param name="expected">The expected string value.</param>
            <param name="actual">The actual string value.</param>
            <exception cref="T:Xunit.Sdk.EqualException">Thrown when the strings are not equivalent.</exception>
        </member>
        <member name="M:Xunit.Assert.Equal(System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Verifies that two strings are equivalent.
            </summary>
            <param name="expected">The expected string value.</param>
            <param name="actual">The actual string value.</param>
            <param name="ignoreCase">If set to <c>true</c>, ignores cases differences. The invariant culture is used.</param>
            <param name="ignoreLineEndingDifferences">If set to <c>true</c>, treats \r\n, \r, and \n as equivalent.</param>
            <param name="ignoreWhiteSpaceDifferences">If set to <c>true</c>, treats spaces and tabs (in any non-zero quantity) as equivalent.</param>
            <param name="ignoreAllWhiteSpace">If set to <c>true</c>, ignores all white space differences during comparison.</param>
            <exception cref="T:Xunit.Sdk.EqualException">Thrown when the strings are not equivalent.</exception>
        </member>
        <member name="M:Xunit.Assert.IsAssignableFrom``1(System.Object)">
            <summary>
            Verifies that an object is of the given type or a derived type.
            </summary>
            <typeparam name="T">The type the object should be</typeparam>
            <param name="object">The object to be evaluated</param>
            <returns>The object, casted to type T when successful</returns>
            <exception cref="T:Xunit.Sdk.IsAssignableFromException">Thrown when the object is not the given type</exception>
        </member>
        <member name="M:Xunit.Assert.IsAssignableFrom(System.Type,System.Object)">
            <summary>
            Verifies that an object is of the given type or a derived type.
            </summary>
            <param name="expectedType">The type the object should be</param>
            <param name="object">The object to be evaluated</param>
            <exception cref="T:Xunit.Sdk.IsAssignableFromException">Thrown when the object is not the given type</exception>
        </member>
        <member name="M:Xunit.Assert.IsNotAssignableFrom``1(System.Object)">
            <summary>
            Verifies that an object is not of the given type or a derived type.
            </summary>
            <typeparam name="T">The type the object should not be</typeparam>
            <param name="object">The object to be evaluated</param>
            <returns>The object, casted to type T when successful</returns>
            <exception cref="T:Xunit.Sdk.IsNotAssignableFromException">Thrown when the object is of the given type</exception>
        </member>
        <member name="M:Xunit.Assert.IsNotAssignableFrom(System.Type,System.Object)">
            <summary>
            Verifies that an object is not of the given type or a derived type.
            </summary>
            <param name="expectedType">The type the object should not be</param>
            <param name="object">The object to be evaluated</param>
            <exception cref="T:Xunit.Sdk.IsNotAssignableFromException">Thrown when the object is of the given type</exception>
        </member>
        <member name="M:Xunit.Assert.IsNotType``1(System.Object)">
            <summary>
            Verifies that an object is not exactly the given type.
            </summary>
            <typeparam name="T">The type the object should not be</typeparam>
            <param name="object">The object to be evaluated</param>
            <exception cref="T:Xunit.Sdk.IsNotTypeException">Thrown when the object is the given type</exception>
        </member>
        <member name="M:Xunit.Assert.IsNotType(System.Type,System.Object)">
            <summary>
            Verifies that an object is not exactly the given type.
            </summary>
            <param name="expectedType">The type the object should not be</param>
            <param name="object">The object to be evaluated</param>
            <exception cref="T:Xunit.Sdk.IsNotTypeException">Thrown when the object is the given type</exception>
        </member>
        <member name="M:Xunit.Assert.IsType``1(System.Object)">
            <summary>
            Verifies that an object is exactly the given type (and not a derived type).
            </summary>
            <typeparam name="T">The type the object should be</typeparam>
            <param name="object">The object to be evaluated</param>
            <returns>The object, casted to type T when successful</returns>
            <exception cref="T:Xunit.Sdk.IsTypeException">Thrown when the object is not the given type</exception>
        </member>
        <member name="M:Xunit.Assert.IsType(System.Type,System.Object)">
            <summary>
            Verifies that an object is exactly the given type (and not a derived type).
            </summary>
            <param name="expectedType">The type the object should be</param>
            <param name="object">The object to be evaluated</param>
            <exception cref="T:Xunit.Sdk.IsTypeException">Thrown when the object is not the given type</exception>
        </member>
        <member name="T:Xunit.Sdk.ArgumentFormatter">
            <summary>
            Formats value for display in assertion messages and data-driven test display names.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.ArgumentFormatter.MAX_DEPTH">
            <summary>
            Gets the maximum printing depth, in terms of objects before truncation.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.ArgumentFormatter.MAX_ENUMERABLE_LENGTH">
            <summary>
            Gets the maximum number of values printed for collections before truncation.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.ArgumentFormatter.MAX_OBJECT_ITEM_COUNT">
            <summary>
            Gets the maximum number of items (properties or fields) printed in an object before truncation.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.ArgumentFormatter.MAX_STRING_LENGTH">
            <summary>
            Gets the maximum strength length before truncation.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.ArgumentFormatter.Ellipsis">
            <summary>
            Gets the ellipsis value (three middle dots, aka U+00B7).
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ArgumentFormatter.EscapeString(System.String)">
            <summary>
            Escapes a string for printing, attempting to most closely model the value on how you would
            enter the value in a C# string literal. That means control codes that are normally backslash
            escaped (like "\n" for newline) are represented like that; all other control codes for ASCII
            values under 32 are printed as "\xnn".
            </summary>
            <param name="s">The string value to be escaped</param>
        </member>
        <member name="M:Xunit.Sdk.ArgumentFormatter.Format(System.Object,System.Int32)">
            <summary>
            Formats a value for display.
            </summary>
            <param name="value">The value to be formatted</param>
            <param name="depth">The optional printing depth (1 indicates a top-level value)</param>
        </member>
        <member name="M:Xunit.Sdk.ArgumentFormatter.FormatTypeName(System.Type,System.Boolean)">
            <summary>
            Formats a type. This maps built-in C# types to their C# native name (e.g., printing "int" instead
            of "Int32" or "System.Int32").
            </summary>
            <param name="type">The type to get the formatted name of</param>
            <param name="fullTypeName">Set to <c>true</c> to include the namespace; set to <c>false</c> for just the simple type name</param>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer.GetDefaultComparer(System.Type)">
            <summary>
            Gets the default comparer to be used for the provided <paramref name="type"/> when a custom one
            has not been provided. Creates an instance of <see cref="T:Xunit.Sdk.AssertEqualityComparer`1"/> wrapped
            by <see cref="T:Xunit.Sdk.AssertEqualityComparerAdapter`1"/>.
            </summary>
            <param name="type">The type to be compared</param>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer.GetDefaultInnerComparer(System.Type)">
            <summary>
            Gets the default comparer to be used as an inner comparer for the provided <paramref name="type"/>
            when a custom one has not been provided. For non-collections, this defaults to an <see cref="T:System.Object"/>-based
            comparer; for collections, this creates an inner comparer based on the item type in the collection.
            </summary>
            <param name="type">The type to create an inner comparer for</param>
        </member>
        <member name="T:Xunit.Sdk.AssertEqualityComparer.OperationalFailureException">
            <summary>
            This exception is thrown when an operation failure has occured during equality comparison operations.
            This generally indicates that a necessary pre-condition was not met for comparison operations to succeed.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer.OperationalFailureException.ForIllegalGetHashCode">
            <summary>
            Gets an exception that indicates that GetHashCode was called on <see cref="T:Xunit.Sdk.AssertEqualityComparer`1.FuncEqualityComparer"/>
            which usually indicates that an item comparison function was used to try to compare two hash sets.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.AssertEqualityComparer`1">
            <summary>
            Default implementation of <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> used by the xUnit.net equality assertions
            (except for collections, which are handled directly by the appropriate assertion methods).
            </summary>
            <typeparam name="T">The type that is being compared.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer`1.#ctor(System.Collections.IEqualityComparer)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.AssertEqualityComparer`1" /> class.
            </summary>
            <param name="innerComparer">The inner comparer to be used when the compared objects are enumerable.</param>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer`1.Equals(`0,`0)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer`1.GetHashCode(`0)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer`1.GuardArgumentNotNull``1(System.String,``0)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.AssertEqualityComparerAdapter`1">
            <summary>
            A class that wraps <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> to add <see cref="T:System.Collections.IEqualityComparer"/>.
            </summary>
            <typeparam name="T">The type that is being compared.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.AssertEqualityComparerAdapter`1"/> class.
            </summary>
            <param name="innerComparer">The comparer that is being adapted.</param>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.Equals(System.Object,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.Equals(`0,`0)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.GetHashCode(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.GetHashCode(`0)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.AssertRangeComparer`1">
            <summary>
            Default implementation of <see cref="T:System.Collections.Generic.IComparer`1"/> used by the xUnit.net range assertions.
            </summary>
            <typeparam name="T">The type that is being compared.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.AssertRangeComparer`1.Compare(`0,`0)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.CollectionTracker">
            <summary>
            Base class for generic <see cref="T:Xunit.Sdk.CollectionTracker`1"/>, which also includes some public
            static functionality.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.#ctor(System.Collections.IEnumerable)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.CollectionTracker"/> class.
            </summary>
            <param name="innerEnumerable"></param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="P:Xunit.Sdk.CollectionTracker.InnerEnumerable">
            <summary>
            Gets the inner enumerable that this collection track is wrapping. This is mostly
            provided for simplifying other APIs which require both the tracker and the collection
            (for example, <see cref="M:Xunit.Sdk.CollectionTracker.AreCollectionsEqual(Xunit.Sdk.CollectionTracker,Xunit.Sdk.CollectionTracker,System.Collections.IEqualityComparer,System.Boolean,System.Nullable{System.Int32}@)"/>).
            </summary>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.AreCollectionsEqual(Xunit.Sdk.CollectionTracker,Xunit.Sdk.CollectionTracker,System.Collections.IEqualityComparer,System.Boolean,System.Nullable{System.Int32}@)">
            <summary>
            Determine if two enumerable collections are equal. It contains logic that varies depending
            on the collection type (supporting arrays, dictionaries, sets, and generic enumerables).
            </summary>
            <param name="x">First value to compare</param>
            <param name="y">Second value to comare</param>
            <param name="itemComparer">The comparer used for individual item comparisons</param>
            <param name="isDefaultItemComparer">Pass <c>true</c> if the <paramref name="itemComparer"/> is the default item
            comparer from <see cref="T:Xunit.Sdk.AssertEqualityComparer`1"/>; pass <c>false</c>, otherwise.</param>
            <param name="mismatchedIndex">The output mismatched item index when the collections are not equal</param>
            <returns>Returns <c>true</c> if the collections are equal; <c>false</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.FormatIndexedMismatch(System.Nullable{System.Int32},System.Nullable{System.Int32}@,System.Int32)">
            <summary>
            Formats the collection when you have a mismatched index. The formatted result will be the section of the
            collection surrounded by the mismatched item.
            </summary>
            <param name="mismatchedIndex">The index of the mismatched item</param>
            <param name="pointerIndent">How many spaces into the output value the pointed-to item begins at</param>
            <param name="depth">The optional printing depth (1 indicates a top-level value)</param>
            <returns>The formatted collection</returns>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.FormatIndexedMismatch(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32}@,System.Int32)">
            <summary>
            Formats the collection when you have a mismatched index. The formatted result will be the section of the
            collection from <paramref name="startIndex"/> to <paramref name="endIndex"/>. These indices are usually
            obtained by calling <see cref="M:Xunit.Sdk.CollectionTracker.GetMismatchExtents(System.Nullable{System.Int32},System.Int32@,System.Int32@)"/>.
            </summary>
            <param name="startIndex">The start index of the collection to print</param>
            <param name="endIndex">The end index of the collection to print</param>
            <param name="mismatchedIndex">The mismatched item index</param>
            <param name="pointerIndent">How many spaces into the output value the pointed-to item begins at</param>
            <param name="depth">The optional printing depth (1 indicates a top-level value)</param>
            <returns>The formatted collection</returns>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.FormatStart(System.Int32)">
            <summary>
            Formats the beginning part of the collection.
            </summary>
            <param name="depth">The optional printing depth (1 indicates a top-level value)</param>
            <returns>The formatted collection</returns>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.GetMismatchExtents(System.Nullable{System.Int32},System.Int32@,System.Int32@)">
            <summary>
            Gets the extents to print when you find a mismatched index, in the form of
            a <paramref name="startIndex"/> and <paramref name="endIndex"/>. If the mismatched
            index is <c>null</c>, the extents will start at index 0.
            </summary>
            <param name="mismatchedIndex">The mismatched item index</param>
            <param name="startIndex">The start index that should be used for printing</param>
            <param name="endIndex">The end index that should be used for printing</param>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.GetSafeEnumerator">
            <summary>
            Gets a safe version of <see cref="T:System.Collections.IEnumerator"/> that prevents double enumeration and does all
            the necessary tracking required for collection formatting. Should should be the same value
            returned by <see cref="M:Xunit.Sdk.CollectionTracker`1.GetEnumerator"/>, except non-generic.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.TypeAt(System.Nullable{System.Int32})">
            <summary>
            Gets the full name of the type of the element at the given index, if known.
            Since this uses the item cache produced by enumeration, it may return <c>null</c>
            when we haven't enumerated enough to see the given element, or if we enumerated
            so much that the item has left the cache, or if the item at the given index
            is <c>null</c>. It will also return <c>null</c> when the <paramref name="index"/>
            is <c>null</c>.
            </summary>
            <param name="index">The item index</param>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker.Wrap(System.Collections.IEnumerable)">
            <summary>
            Wraps an untyped enumerable in an object-based <see cref="T:Xunit.Sdk.CollectionTracker`1"/>.
            </summary>
            <param name="enumerable">The untyped enumerable to wrap</param>
        </member>
        <member name="T:Xunit.Sdk.CollectionTracker`1">
            <summary>
            A utility class that can be used to wrap enumerables to prevent double enumeration.
            It offers the ability to safely print parts of the collection when failures are
            encountered, as well as some static versions of the printing functionality.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.#ctor(System.Collections.IEnumerable,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            INTERNAL CONSTRUCTOR. DO NOT CALL.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.CollectionTracker`1.IterationCount">
            <summary>
            Gets the number of iterations that have happened so far.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.FormatIndexedMismatch(System.Nullable{System.Int32},System.Nullable{System.Int32}@,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.FormatIndexedMismatch(System.Int32,System.Int32,System.Nullable{System.Int32},System.Nullable{System.Int32}@,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.FormatStart(System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.FormatStart(System.Collections.Generic.IEnumerable{`0},System.Int32)">
            <summary>
            Formats the beginning part of a collection.
            </summary>
            <param name="collection">The collection to be formatted</param>
            <param name="depth">The optional printing depth (1 indicates a top-level value)</param>
            <returns>The formatted collection</returns>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.GetSafeEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.GetMismatchExtents(System.Nullable{System.Int32},System.Int32@,System.Int32@)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.TypeAt(System.Nullable{System.Int32})">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionTracker`1.Wrap(System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Wraps the given collection inside of a <see cref="T:Xunit.Sdk.CollectionTracker`1"/>.
            </summary>
            <param name="collection">The collection to be wrapped</param>
        </member>
        <member name="T:Xunit.Sdk.CollectionTrackerExtensions">
            <summary>
            Extension methods related to <see cref="T:Xunit.Sdk.CollectionTracker`1"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.CollectionTrackerExtensions.AsTracker(System.Collections.IEnumerable)">
            <summary>
            Wraps the given enumerable in an instance of <see cref="T:Xunit.Sdk.CollectionTracker`1"/>.
            </summary>
            <param name="enumerable">The enumerable to be wrapped</param>
        </member>
        <member name="M:Xunit.Sdk.CollectionTrackerExtensions.AsTracker``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Wraps the given enumerable in an instance of <see cref="T:Xunit.Sdk.CollectionTracker`1"/>.
            </summary>
            <typeparam name="T">The item type of the collection</typeparam>
            <param name="enumerable">The enumerable to be wrapped</param>
        </member>
        <member name="M:Xunit.Sdk.CollectionTrackerExtensions.GetEnumerator(Xunit.Sdk.CollectionTracker)">
            <summary>
            Enumerates the elements inside the collection tracker.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.DynamicSkipToken.Value">
            <summary>
            The contract for exceptions which indicate that something should be skipped rather than
            failed is that exception message should start with this, and that any text following this
            will be treated as the skip reason (for example,
            "$XunitDynamicSkip$This code can only run on Linux") will result in a skipped test with
            the reason of "This code can only run on Linux".
            </summary>
        </member>
        <member name="T:Xunit.Sdk.AllException">
            <summary>
            Exception thrown when Assert.All fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.AllException.ForFailures(System.Int32,System.Collections.Generic.IReadOnlyList{System.Tuple{System.Int32,System.String,System.Exception}})">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.AllException"/> class to be thrown when one or
            more items failed during <see cref="M:Xunit.Assert.All``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})"/>
            or <see cref="M:Xunit.Assert.All``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0,System.Int32})"/>,
            <see cref="M:Xunit.Assert.AllAsync``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Threading.Tasks.Task})"/>,
            or <see cref="M:Xunit.Assert.AllAsync``1(System.Collections.Generic.IEnumerable{``0},System.Func{``0,System.Int32,System.Threading.Tasks.Task})"/>.
            </summary>
            <param name="totalItems">The total number of items in the collection</param>
            <param name="errors">The list of failures (as index, value, and exception)</param>
        </member>
        <member name="T:Xunit.Sdk.CollectionException">
            <summary>
            Exception thrown when Assert.Collection fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.CollectionException.ForMismatchedItem(System.Exception,System.Int32,System.Nullable{System.Int32},System.String)">
            <summary>
            Creates an instance of the <see cref="T:Xunit.Sdk.CollectionException"/> class to be thrown
            when an item comparison failed
            </summary>
            <param name="exception">The exception that was thrown</param>
            <param name="indexFailurePoint">The item index for the failed item</param>
            <param name="failurePointerIndent">The number of spaces needed to indent the failure pointer</param>
            <param name="formattedCollection">The formatted collection</param>
        </member>
        <member name="M:Xunit.Sdk.CollectionException.ForMismatchedItemCount(System.Int32,System.Int32,System.String)">
            <summary>
            Creates an instance of the <see cref="T:Xunit.Sdk.CollectionException"/> class to be thrown
            when the item count in a collection does not match the expected count.
            </summary>
            <param name="expectedCount">The expected item count</param>
            <param name="actualCount">The actual item count</param>
            <param name="formattedCollection">The formatted collection</param>
        </member>
        <member name="T:Xunit.Sdk.ContainsException">
            <summary>
            Exception thrown when Assert.Contains fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ContainsException.ForCollectionFilterNotMatched(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ContainsException"/> class to be thrown
            when the requested filter did not match any items in the collection.
            </summary>
            <param name="collection">The collection</param>
        </member>
        <member name="M:Xunit.Sdk.ContainsException.ForCollectionItemNotFound(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ContainsException"/> class to be thrown
            when the requested item was not available in the collection.
            </summary>
            <param name="item">The expected item value</param>
            <param name="collection">The collection</param>
        </member>
        <member name="M:Xunit.Sdk.ContainsException.ForKeyNotFound(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ContainsException"/> class to be thrown
            when the requested key was not available in the dictionary.
            </summary>
            <param name="expectedKey">The expected key value</param>
            <param name="keys">The dictionary keys</param>
        </member>
        <member name="M:Xunit.Sdk.ContainsException.ForSetItemNotFound(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ContainsException"/> class to be thrown
            when the requested item was not found in the set.
            </summary>
            <param name="item">The expected item</param>
            <param name="set">The set</param>
        </member>
        <member name="M:Xunit.Sdk.ContainsException.ForSubMemoryNotFound(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ContainsException"/> class to be thrown
            when the requested sub-memory was not found in the memory.
            </summary>
            <param name="expectedSubMemory">The expected sub-memory</param>
            <param name="memory">The memory</param>
        </member>
        <member name="M:Xunit.Sdk.ContainsException.ForSubSpanNotFound(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ContainsException"/> class to be thrown
            when the requested sub-span was not found in the span.
            </summary>
            <param name="expectedSubSpan">The expected sub-span</param>
            <param name="span">The span</param>
        </member>
        <member name="M:Xunit.Sdk.ContainsException.ForSubStringNotFound(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ContainsException"/> class to be thrown
            when the requested sub-string was not found in the string.
            </summary>
            <param name="expectedSubString">The expected sub-string</param>
            <param name="string">The string</param>
        </member>
        <member name="T:Xunit.Sdk.DistinctException">
            <summary>
            Exception thrown when Assert.Distinct fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DistinctException.ForDuplicateItem(System.String,System.String)">
            <summary>
            Creates an instance of the <see cref="T:Xunit.Sdk.DistinctException"/> class that is thrown
            when a duplicate item is found in a collection.
            </summary>
            <param name="item">The duplicate item</param>
            <param name="collection">The collection</param>
        </member>
        <member name="T:Xunit.Sdk.DoesNotContainException">
            <summary>
            Exception thrown when Assert.DoesNotContain fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DoesNotContainException.ForCollectionFilterMatched(System.Int32,System.Nullable{System.Int32},System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.DoesNotContainException"/> class to be thrown
            when the requested filter matches an item in the collection.
            </summary>
            <param name="indexFailurePoint">The item index for where the item was found</param>
            <param name="failurePointerIndent">The number of spaces needed to indent the failure pointer</param>
            <param name="collection">The collection</param>
        </member>
        <member name="M:Xunit.Sdk.DoesNotContainException.ForCollectionItemFound(System.String,System.Int32,System.Nullable{System.Int32},System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.DoesNotContainException"/> class to be thrown
            when the requested item was found in the collection.
            </summary>
            <param name="item">The item that was found in the collection</param>
            <param name="indexFailurePoint">The item index for where the item was found</param>
            <param name="failurePointerIndent">The number of spaces needed to indent the failure pointer</param>
            <param name="collection">The collection</param>
        </member>
        <member name="M:Xunit.Sdk.DoesNotContainException.ForKeyFound(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.DoesNotContainException"/> class to be thrown
            when the requested key was found in the dictionary.
            </summary>
            <param name="expectedKey">The expected key value</param>
            <param name="keys">The dictionary keys</param>
        </member>
        <member name="M:Xunit.Sdk.DoesNotContainException.ForSetItemFound(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.DoesNotContainException"/> class to be thrown
            when the requested item was found in the set.
            </summary>
            <param name="item">The item that was found in the collection</param>
            <param name="set">The set</param>
        </member>
        <member name="M:Xunit.Sdk.DoesNotContainException.ForSubMemoryFound(System.String,System.Int32,System.Nullable{System.Int32},System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.DoesNotContainException"/> class to be thrown
            when the requested sub-memory was found in the memory.
            </summary>
            <param name="expectedSubMemory">The expected sub-memory</param>
            <param name="indexFailurePoint">The item index for where the item was found</param>
            <param name="failurePointerIndent">The number of spaces needed to indent the failure pointer</param>
            <param name="memory">The memory</param>
        </member>
        <member name="M:Xunit.Sdk.DoesNotContainException.ForSubSpanFound(System.String,System.Int32,System.Nullable{System.Int32},System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.DoesNotContainException"/> class to be thrown
            when the requested sub-span was found in the span.
            </summary>
            <param name="expectedSubSpan">The expected sub-span</param>
            <param name="indexFailurePoint">The item index for where the item was found</param>
            <param name="failurePointerIndent">The number of spaces needed to indent the failure pointer</param>
            <param name="span">The span</param>
        </member>
        <member name="M:Xunit.Sdk.DoesNotContainException.ForSubStringFound(System.String,System.Int32,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.DoesNotContainException"/> class to be thrown
            when the requested sub-string was found in the string.
            </summary>
            <param name="expectedSubString">The expected sub-string</param>
            <param name="indexFailurePoint">The item index for where the item was found</param>
            <param name="string">The string</param>
        </member>
        <member name="T:Xunit.Sdk.DoesNotMatchException">
            <summary>
            Exception thrown when Assert.DoesNotMatch fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DoesNotMatchException.ForMatch(System.String,System.Int32,System.Int32,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.DoesNotMatchException"/> class, thrown when
            a regular expression matches the input string.
            </summary>
            <param name="expectedRegexPattern">The expected regular expression pattern</param>
            <param name="indexFailurePoint">The item index for where the item was found</param>
            <param name="failurePointerIndent">The number of spaces needed to indent the failure pointer</param>
            <param name="string">The string matched again</param>
            <exception cref="T:System.InvalidOperationException"></exception>
        </member>
        <member name="T:Xunit.Sdk.EmptyException">
            <summary>
            Exception thrown when Assert.Empty fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.EmptyException.ForNonEmptyCollection(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.EmptyException"/> to be thrown
            when the collection is not empty.
            </summary>
            <param name="collection">The non-empty collection</param>
        </member>
        <member name="M:Xunit.Sdk.EmptyException.ForNonEmptyString(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.EmptyException"/> to be thrown
            when the string is not empty.
            </summary>
            <param name="value">The non-empty string value</param>
        </member>
        <member name="T:Xunit.Sdk.EndsWithException">
            <summary>
            Exception thrown when Assert.EndsWith fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.EndsWithException.ForStringNotFound(System.String,System.String)">
            <summary>
            Creates an instance of the <see cref="T:Xunit.Sdk.EndsWithException"/> class to be thrown
            when a string does not end with the given value.
            </summary>
            <param name="expected">The expected ending</param>
            <param name="actual">The actual value</param>
            <returns></returns>
        </member>
        <member name="T:Xunit.Sdk.EqualException">
            <summary>
            Exception thrown when Assert.Equal fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.EqualException.ForMismatchedCollections(System.Nullable{System.Int32},System.String,System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Int32},System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EqualException"/> to be thrown when two collections
            are not equal.
            </summary>
            <param name="mismatchedIndex">The index at which the collections differ</param>
            <param name="expected">The expected collection</param>
            <param name="expectedPointer">The spacing into the expected collection where the difference occurs</param>
            <param name="expectedType">The type of the expected collection items, when they differ in type</param>
            <param name="actual">The actual collection</param>
            <param name="actualPointer">The spacing into the actual collection where the difference occurs</param>
            <param name="actualType">The type of the actual collection items, when they differ in type</param>
            <param name="collectionDisplay">The display name for the collection type (defaults to "Collections")</param>
        </member>
        <member name="M:Xunit.Sdk.EqualException.ForMismatchedCollectionsWithError(System.Nullable{System.Int32},System.String,System.Nullable{System.Int32},System.String,System.String,System.Nullable{System.Int32},System.String,System.Exception,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EqualException"/> to be thrown when two collections
            are not equal, and an error has occurred during comparison.
            </summary>
            <param name="mismatchedIndex">The index at which the collections differ</param>
            <param name="expected">The expected collection</param>
            <param name="expectedPointer">The spacing into the expected collection where the difference occurs</param>
            <param name="expectedType">The type of the expected collection items, when they differ in type</param>
            <param name="actual">The actual collection</param>
            <param name="actualPointer">The spacing into the actual collection where the difference occurs</param>
            <param name="actualType">The type of the actual collection items, when they differ in type</param>
            <param name="error">The optional exception that was thrown during comparison</param>
            <param name="collectionDisplay">The display name for the collection type (defaults to "Collections")</param>
        </member>
        <member name="M:Xunit.Sdk.EqualException.ForMismatchedStrings(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EqualException"/> to be thrown when two string
            values are not equal.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
            <param name="expectedIndex">The index point in the expected string where the values differ</param>
            <param name="actualIndex">The index point in the actual string where the values differ</param>
        </member>
        <member name="M:Xunit.Sdk.EqualException.ForMismatchedValues(System.Object,System.Object,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EqualException"/> to be thrown when two values
            are not equal. This may be simple values (like intrinsics) or complex values (like
            classes or structs).
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
            <param name="banner">The banner to show; if <c>null</c>, then the standard
            banner of "Values differ" will be used</param>
        </member>
        <member name="M:Xunit.Sdk.EqualException.ForMismatchedValuesWithError(System.Object,System.Object,System.Exception,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EqualException"/> to be thrown when two values
            are not equal. This may be simple values (like intrinsics) or complex values (like
            classes or structs). Used when an error has occurred during comparison.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
            <param name="error">The optional exception that was thrown during comparison</param>
            <param name="banner">The banner to show; if <c>null</c>, then the standard
            banner of "Values differ" will be used. If <paramref name="error"/> is not <c>null</c>,
            then the banner used will always be "Exception thrown during comparison", regardless
            of the value passed here.</param>
        </member>
        <member name="T:Xunit.Sdk.EquivalentException">
            <summary>
            Exception thrown when Assert.Equivalent fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.EquivalentException.ForCircularReference(System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EquivalentException"/> which shows a message that indicates
            a circular reference was discovered.
            </summary>
            <param name="memberName">The name of the member that caused the circular reference</param>
        </member>
        <member name="M:Xunit.Sdk.EquivalentException.ForExceededDepth(System.Int32,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EquivalentException"/> which shows a message that indicates
            that the maximum comparison depth was exceeded.
            </summary>
            <param name="depth">The depth reached</param>
            <param name="memberName">The member access which caused the failure</param>
        </member>
        <member name="M:Xunit.Sdk.EquivalentException.ForMemberListMismatch(System.Collections.Generic.IEnumerable{System.String},System.Collections.Generic.IEnumerable{System.String},System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EquivalentException"/> which shows a message that indicates
            that the list of available members does not match.
            </summary>
            <param name="expectedMemberNames">The expected member names</param>
            <param name="actualMemberNames">The actual member names</param>
            <param name="prefix">The prefix to be applied to the member names (may be an empty string for a
            top-level object, or a name in "member." format used as a prefix to show the member name list)</param>
        </member>
        <member name="M:Xunit.Sdk.EquivalentException.ForMemberValueMismatch(System.Object,System.Object,System.String,System.Exception)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EquivalentException"/> which shows a message that indicates
            that the fault comes from an individual value mismatch one of the members.
            </summary>
            <param name="expected">The expected member value</param>
            <param name="actual">The actual member value</param>
            <param name="memberName">The name of the mismatched member (may be an empty string for a
            top-level object)</param>
            <param name="innerException">The inner exception that was thrown during value comparison,
            typically during a call to <see cref="M:System.IComparable.CompareTo(System.Object)"/></param>
        </member>
        <member name="M:Xunit.Sdk.EquivalentException.ForMissingCollectionValue(System.Object,System.Collections.Generic.IEnumerable{System.Object},System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EquivalentException"/> which shows a message that indicates
            a value was missing from the <paramref name="actual"/> collection.
            </summary>
            <param name="expected">The object that was expected to be found in <paramref name="actual"/> collection.</param>
            <param name="actual">The actual collection which was missing the object.</param>
            <param name="memberName">The name of the member that was being inspected (may be an empty
            string for a top-level collection)</param>
        </member>
        <member name="M:Xunit.Sdk.EquivalentException.ForExtraCollectionValue(System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.Object},System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EquivalentException"/> which shows a message that indicates
            that <paramref name="actual"/> contained one or more values that were not specified
            in <paramref name="expected"/>.
            </summary>
            <param name="expected">The values expected to be found in the <paramref name="actual"/>
            collection.</param>
            <param name="actual">The actual collection values.</param>
            <param name="actualLeftovers">The values from <paramref name="actual"/> that did not have
            matching <paramref name="expected"/> values</param>
            <param name="memberName">The name of the member that was being inspected (may be an empty
            string for a top-level collection)</param>
        </member>
        <member name="M:Xunit.Sdk.EquivalentException.ForMismatchedTypes(System.Type,System.Type,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.EquivalentException"/> which shows a message that indicates
            that <paramref name="expectedType"/> does not match <paramref name="actualType"/>. This is typically
            only used in special case comparison where it would be known that general comparison would fail
            for other reasons, like two objects derived from <see cref="T:System.IO.FileSystemInfo"/> with
            different concrete types.
            </summary>
            <param name="expectedType">The expected type</param>
            <param name="actualType">The actual type</param>
            <param name="memberName">The name of the member that was being inspected (may be an empty
            string for a top-level comparison)</param>
        </member>
        <member name="T:Xunit.Sdk.FailException">
            <summary>
            Exception thrown when Assert.Fail is called.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.FailException.ForFailure(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.FailException"/> class to be thrown when
            the user calls <see cref="M:Xunit.Assert.Fail(System.String)"/>.
            </summary>
            <param name="message">The user's failure message.</param>
        </member>
        <member name="T:Xunit.Sdk.FalseException">
            <summary>
            Exception thrown when Assert.False fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.FalseException.ForNonFalseValue(System.String,System.Nullable{System.Boolean})">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.FalseException"/> class to be thrown when
            a non-<c>false</c> value was provided.
            </summary>
            <param name="message">The message to be displayed, or <c>null</c> for the default message</param>
            <param name="value">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.InRangeException">
            <summary>
            Exception thrown when Assert.InRange fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.InRangeException.ForValueNotInRange(System.Object,System.Object,System.Object)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.InRangeException"/> class to be thrown when
            the given value is not in the given range.
            </summary>
            <param name="actual">The actual object value</param>
            <param name="low">The low value of the range</param>
            <param name="high">The high value of the range</param>
        </member>
        <member name="T:Xunit.Sdk.IsAssignableFromException">
            <summary>
            Exception thrown when Assert.IsAssignableFrom fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.IsAssignableFromException.ForIncompatibleType(System.Type,System.Object)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.IsTypeException"/> class to be thrown when
            the value is not compatible with the given type.
            </summary>
            <param name="expected">The expected type</param>
            <param name="actual">The actual object value</param>
        </member>
        <member name="T:Xunit.Sdk.IsNotAssignableFromException">
            <summary>
            Exception thrown when Assert.IsNotAssignableFrom fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.IsNotAssignableFromException.ForCompatibleType(System.Type,System.Object)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.IsNotAssignableFromException"/> class to be thrown when
            the value is compatible with the given type.
            </summary>
            <param name="expected">The expected type</param>
            <param name="actual">The actual object value</param>
        </member>
        <member name="T:Xunit.Sdk.IsNotTypeException">
            <summary>
            Exception thrown when Assert.IsNotType fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.IsNotTypeException.ForExactType(System.Type)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.IsNotTypeException"/> class to be thrown
            when the object is the exact type.
            </summary>
            <param name="type">The expected type</param>
        </member>
        <member name="T:Xunit.Sdk.IsTypeException">
            <summary>
            Exception thrown when Assert.IsType fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.IsTypeException.ForMismatchedType(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.IsTypeException"/> class to be thrown
            when an object did not exactly match the given type
            </summary>
            <param name="expectedTypeName">The expected type name</param>
            <param name="actualTypeName">The actual type name</param>
        </member>
        <member name="T:Xunit.Sdk.MatchesException">
            <summary>
            Exception thrown when Assert.Matches fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.MatchesException.ForMatchNotFound(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.MatchesException"/> class to be thrown when
            the regular expression pattern isn't found within the value.
            </summary>
            <param name="expectedRegexPattern">The expected regular expression pattern</param>
            <param name="actual">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.MultipleException">
            <summary>
            Exception thrown when Assert.Multiple fails w/ multiple errors (when a single error
            occurs, it is thrown directly).
            </summary>
        </member>
        <member name="P:Xunit.Sdk.MultipleException.InnerExceptions">
            <summary>
            Gets the list of inner exceptions that were thrown.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.MultipleException.StackTrace">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.MultipleException.ForFailures(System.Collections.Generic.IReadOnlyCollection{System.Exception})">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.MultipleException"/> class to be thrown
            when <see cref="M:Xunit.Assert.Multiple(System.Action[])"/> caught 2 or more exceptions.
            </summary>
            <param name="innerExceptions">The inner exceptions</param>
        </member>
        <member name="T:Xunit.Sdk.NotEmptyException">
            <summary>
            Exception thrown when Assert.NotEmpty fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.NotEmptyException.ForNonEmptyCollection">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.NotEmptyException"/> class to be thrown
            when a collection was unexpectedly empty.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.NotEqualException">
            <summary>
            Exception thrown when Assert.NotEqual fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.NotEqualException.ForEqualCollections(System.String,System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.NotEqualException"/> to be thrown when two collections
            are equal.
            </summary>
            <param name="expected">The expected collection</param>
            <param name="actual">The actual collection</param>
            <param name="collectionDisplay">The display name for the collection type (defaults to "Collections")</param>
        </member>
        <member name="M:Xunit.Sdk.NotEqualException.ForEqualCollectionsWithError(System.Nullable{System.Int32},System.String,System.Nullable{System.Int32},System.String,System.Nullable{System.Int32},System.Exception,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.NotEqualException"/> to be thrown when two collections
            are equal, and an error has occurred during comparison.
            </summary>
            <param name="mismatchedIndex">The index at which the collections error occurred (should be <c>null</c>
            when <paramref name="error"/> is <c>null</c>)</param>
            <param name="expected">The expected collection</param>
            <param name="expectedPointer">The spacing into the expected collection where the difference occurs
            (should be <c>null</c> when <paramref name="error"/> is null)</param>
            <param name="actual">The actual collection</param>
            <param name="actualPointer">The spacing into the actual collection where the difference occurs
            (should be <c>null</c> when <paramref name="error"/> is null)</param>
            <param name="error">The optional exception that was thrown during comparison</param>
            <param name="collectionDisplay">The display name for the collection type (defaults to "Collections")</param>
        </member>
        <member name="M:Xunit.Sdk.NotEqualException.ForEqualValues(System.String,System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.NotEqualException"/> to be thrown when two values
            are equal. This may be simple values (like intrinsics) or complex values (like
            classes or structs).
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
            <param name="banner">The banner to show; if <c>null</c>, then the standard
            banner of "Values are equal" will be used</param>
        </member>
        <member name="M:Xunit.Sdk.NotEqualException.ForEqualValuesWithError(System.String,System.String,System.Exception,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.NotEqualException"/> to be thrown when two values
            are equal. This may be simple values (like intrinsics) or complex values (like
            classes or structs). Used when an error has occurred during comparison.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
            <param name="error">The optional exception that was thrown during comparison</param>
            <param name="banner">The banner to show; if <c>null</c>, then the standard
            banner of "Values are equal" will be used. If <paramref name="error"/> is not <c>null</c>,
            then the banner used will always be "Exception thrown during comparison", regardless
            of the value passed here.</param>
        </member>
        <member name="T:Xunit.Sdk.NotInRangeException">
            <summary>
            Exception thrown when Assert.NotInRange fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.NotInRangeException.ForValueInRange(System.Object,System.Object,System.Object)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.NotInRangeException"/> class to be thrown when
            a value was unexpected with the range of two other values.
            </summary>
            <param name="actual">The actual object value</param>
            <param name="low">The low value of the range</param>
            <param name="high">The high value of the range</param>
        </member>
        <member name="T:Xunit.Sdk.NotNullException">
            <summary>
            Exception thrown when Assert.NotNull fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.NotNullException.ForNullStruct(System.Type)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.NotNullException"/> class to be
            throw when a nullable struct is <c>null</c>.
            </summary>
            <param name="type">The inner type of the value</param>
        </member>
        <member name="M:Xunit.Sdk.NotNullException.ForNullValue">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.NotNullException"/> class to be
            thrown when a reference value is <c>null</c>.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.NotSameException">
            <summary>
            Exception thrown when Assert.NotSame fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.NotSameException.ForSameValues">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.NotSameException"/> class to be thrown
            when two values are the same instance.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.NotStrictEqualException">
            <summary>
            Exception thrown when Assert.NotStrictEqual fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.NotStrictEqualException.ForEqualValues(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.NotStrictEqualException"/> to be thrown when two values
            are strictly equal.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.NullException">
            <summary>
            Exception thrown when Assert.Null fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.NullException.ForNonNullStruct``1(System.Type,System.Nullable{``0})">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.NullException"/> class to be thrown
            when the given nullable struct was unexpectedly not null.
            </summary>
            <param name="type">The inner type of the value</param>
            <param name="actual">The actual non-<c>null</c> value</param>
        </member>
        <member name="M:Xunit.Sdk.NullException.ForNonNullValue(System.Object)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.NullException"/> class to be thrown
            when the given value was unexpectedly not null.
            </summary>
            <param name="actual">The actual non-<c>null</c> value</param>
        </member>
        <member name="T:Xunit.Sdk.ProperSubsetException">
            <summary>
            Exception thrown when Assert.ProperSubset fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ProperSubsetException.ForFailure(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ProperSubsetException"/> class to be thrown
            when a set is not a proper subset of another set
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.ProperSupersetException">
            <summary>
            Exception thrown when Assert.ProperSuperset fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ProperSupersetException.ForFailure(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ProperSupersetException"/> class to be thrown
            when a set is not a proper superset of another set
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.PropertyChangedException">
            <summary>
            Exception thrown when Assert.PropertyChanged fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.PropertyChangedException.ForUnsetProperty(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.PropertyChangedException"/> class to be thrown
            when a property was unexpectedly not set.
            </summary>
            <param name="propertyName">The name of the property that was expected to be changed.</param>
        </member>
        <member name="T:Xunit.Sdk.RaisesAnyException">
            <summary>
            Exception thrown when Assert.RaisesAny fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.RaisesAnyException.ForNoEvent(System.Type)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.RaisesAnyException" /> class to be thrown when
            no event was raised.
            </summary>
            <param name="expected">The type of the event args that was expected</param>
        </member>
        <member name="T:Xunit.Sdk.RaisesException">
            <summary>
            Exception thrown when Assert.Raises fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.RaisesException.ForIncorrectType(System.Type,System.Type)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.RaisesException" /> class to be thrown when
            the raised event wasn't the expected type.
            </summary>
            <param name="expected">The type of the event args that was expected</param>
            <param name="actual">The type of the event args that was actually raised</param>
        </member>
        <member name="M:Xunit.Sdk.RaisesException.ForNoEvent(System.Type)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.RaisesException" /> class to be thrown when
            no event was raised.
            </summary>
            <param name="expected">The type of the event args that was expected</param>
        </member>
        <member name="T:Xunit.Sdk.SameException">
            <summary>
            Exception thrown when Assert.Same fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.SameException.ForFailure(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.SameException"/> class to be thrown
            when two values are not the same instance.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.SingleException">
            <summary>
            Exception thrown when Assert.Single fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.SingleException.Empty(System.String,System.String)">
            <summary>
            Creates an new instance of the <see cref="T:Xunit.Sdk.SingleException"/> class to be thrown when
            the collection didn't contain any values (or didn't contain the expected value).
            </summary>
            <param name="expected">The expected value (set to <c>null</c> for no expected value)</param>
            <param name="collection">The collection</param>
        </member>
        <member name="M:Xunit.Sdk.SingleException.MoreThanOne(System.Int32,System.String,System.String,System.Collections.Generic.ICollection{System.Int32})">
            <summary>
            Creates an new instance of the <see cref="T:Xunit.Sdk.SingleException"/> class to be thrown when
            the collection more than one value (or contained more than one of the expected value).
            </summary>
            <param name="count">The number of items, or the number of matching items</param>
            <param name="expected">The expected value (set to <c>null</c> for no expected value)</param>
            <param name="collection">The collection</param>
            <param name="matchIndices">The list of indices where matches occurred</param>
        </member>
        <member name="T:Xunit.Sdk.SkipException">
            <summary>
            Exception thrown when Assert.Skip is called.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.SkipException.ForSkip(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.SkipException"/> class to be thrown
            when a user wants to dynamically skip a test. Note that this only works in
            v3 and later of xUnit.net, as it requires runtime infrastructure changes.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.StartsWithException">
            <summary>
            Exception thrown when Assert.StartsWith fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.StartsWithException.ForStringNotFound(System.String,System.String)">
            <summary>
            Creates an instance of the <see cref="T:Xunit.Sdk.StartsWithException"/> class to be thrown
            when a string does not start with the given value.
            </summary>
            <param name="expected">The expected start</param>
            <param name="actual">The actual value</param>
            <returns></returns>
        </member>
        <member name="T:Xunit.Sdk.StrictEqualException">
            <summary>
            Exception thrown when Assert.StrictEqual fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.StrictEqualException.ForEqualValues(System.String,System.String)">
            <summary>
            Creates a new instance of <see cref="T:Xunit.Sdk.StrictEqualException"/> to be thrown when two values
            are not strictly equal.
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.SubsetException">
            <summary>
            Exception thrown when Assert.Subset fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.SubsetException.ForFailure(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.SubsetException"/> class to be thrown
            when a set is not a subset of another set
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.SupersetException">
            <summary>
            Exception thrown when Assert.Superset fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.SupersetException.ForFailure(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.SupersetException"/> class to be thrown
            when a set is not a superset of another set
            </summary>
            <param name="expected">The expected value</param>
            <param name="actual">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.ThrowsAnyException">
            <summary>
            Exception thrown when Assert.ThrowsAny fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ThrowsAnyException.ForIncorrectExceptionType(System.Type,System.Exception)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ThrowsAnyException"/> class to be thrown when
            an exception of the wrong type was thrown by Assert.ThrowsAny.
            </summary>
            <param name="expected">The expected exception type</param>
            <param name="actual">The actual exception</param>
        </member>
        <member name="M:Xunit.Sdk.ThrowsAnyException.ForNoException(System.Type)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ThrowsAnyException"/> class to be thrown when
            an exception wasn't thrown by Assert.ThrowsAny.
            </summary>
            <param name="expected">The expected exception type</param>
        </member>
        <member name="T:Xunit.Sdk.ThrowsException">
            <summary>
            Exception thrown when Assert.Throws fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ThrowsException.ForIncorrectExceptionType(System.Type,System.Exception)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ThrowsException"/> class to be thrown when
            an exception of the wrong type was thrown by Assert.Throws.
            </summary>
            <param name="expected">The expected exception type</param>
            <param name="actual">The actual exception</param>
        </member>
        <member name="M:Xunit.Sdk.ThrowsException.ForIncorrectParameterName(System.Type,System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ThrowsException"/> class to be thrown when
            an <see cref="T:System.ArgumentException"/> is thrown with the wrong parameter name.
            </summary>
            <param name="expected">The exception type</param>
            <param name="expectedParamName">The expected parameter name</param>
            <param name="actualParamName">The actual parameter name</param>
        </member>
        <member name="M:Xunit.Sdk.ThrowsException.ForNoException(System.Type)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.ThrowsException"/> class to be thrown when
            an exception wasn't thrown by Assert.Throws.
            </summary>
            <param name="expected">The expected exception type</param>
        </member>
        <member name="T:Xunit.Sdk.TrueException">
            <summary>
            Exception thrown when Assert.True fails.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TrueException.ForNonTrueValue(System.String,System.Nullable{System.Boolean})">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.Sdk.TrueException"/> class to be thrown when
            a non-<c>true</c> value was provided.
            </summary>
            <param name="message">The message to be displayed, or <c>null</c> for the default message</param>
            <param name="value">The actual value</param>
        </member>
        <member name="T:Xunit.Sdk.XunitException">
            <summary>
            The base assert exception class. It marks itself with <see cref="T:Xunit.Sdk.IAssertionException"/> which is how
            the framework differentiates between assertion fails and general exceptions.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitException"/> class.
            </summary>
            <param name="userMessage">The user message to be displayed</param>
        </member>
        <member name="M:Xunit.Sdk.XunitException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitException"/> class.
            </summary>
            <param name="userMessage">The user message to be displayed</param>
            <param name="innerException">The inner exception</param>
        </member>
        <member name="M:Xunit.Sdk.XunitException.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.IAssertionException">
            <summary>
            This is a marker interface implemented by all built-in assertion exceptions so that
            test failures can be marked with <see cref="F:Xunit.v3.FailureCause.Assertion"/>.
            </summary>
        </member>
    </members>
</doc>
