@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

set "SOURCE_DIR=pic\image"
set "DEST_DIR=output_images"
set "SN_FILE=test.txt"

if not exist "%DEST_DIR%" (
    mkdir "%DEST_DIR%"
)

if not exist "%SN_FILE%" (
    echo Error: %SN_FILE% not found.
    pause
    exit /b 1
)

if not exist "%SOURCE_DIR%" (
    echo Error: Source directory %SOURCE_DIR% not found.
    pause
    exit /b 1
)

for /f "usebackq delims=" %%i in ("%SN_FILE%") do (
    set "SN=%%i"
    echo Searching for files with SN: !SN!
    for /r "%SOURCE_DIR%" %%f in ("*!SN!*") do (
        echo Found file: %%f
        copy "%%f" "%DEST_DIR%\"
    )
)

echo.
echo Script finished. Files have been copied to %DEST_DIR%.
pause
