﻿using log4net.Core;
using Nreal_ProductLine_Tool.PBS;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace image_test
{
    public partial class entrance : Form
    {
        List<Form> forms = new List<Form>();
        int curIndex = 0;
        protected SemaphoreSlim message_sem;

        public entrance()
        {
            InitializeComponent();
            this.Load += MainForm_Load;
            //this.FormClosed += MainForm_FormClosed;
            message_sem = new SemaphoreSlim(0, 1);
            ImageRes.getResource();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            Form form;
            this.WindowState = FormWindowState.Maximized;
            // 获取屏幕工作区大小（去除任务栏等占用空间后的可用屏幕区域）
            Rectangle screenBounds = Screen.FromControl(this).WorkingArea;
            // 设置mainPanel的大小和位置，使其填满除去panel1后的屏幕空间
            mainPanel.Size = new Size(screenBounds.Width- panel1.Width-10, screenBounds.Height);
            mainPanel.Location = new Point(0, 0);
            panel1.Location = new Point(screenBounds.Width - panel1.Width, 0);

            int index = 0;

            form = new pbs();
            form.Size = mainPanel.ClientSize;
            forms.Add(form);

            form = new support();
            form.Size = mainPanel.ClientSize;
            forms.Add(form);

            form = new spb();
            form.Size = mainPanel.ClientSize;
            forms.Add(form);

            form = new prism();
            form.Size = mainPanel.ClientSize;
            forms.Add(form);

            form = new pb();
            form.Size = mainPanel.ClientSize;
            forms.Add(form);

            form = new support_precheck();
            form.Size = mainPanel.ClientSize;
            forms.Add(form);
        }

        public int runColorTest1()
        {
            int img_pos = 1;
            Form frm = null;
                frm = new Form4();

            while (img_pos <= ImageRes.files1.Count)
            {                
                ((Form4)frm).image_change(img_pos, 0);
                ((Form4)frm).imageShowing();
                message_sem.Wait();
                img_pos++;
            }

            frm.Dispose();
            return 0;
        }

        public void Finish(int sequence, int type)
        {
            if (mainPanel.Controls.Count > 0)
                mainPanel.Controls.Remove(forms[curIndex]);
            //forms[sequence].Hide();
            curIndex = sequence;
            mainPanel.Controls.Add(forms[curIndex]);
            forms[curIndex].Show();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //Finish(0, 0);
        }

        private void button2_Click(object sender, EventArgs e)
        {
            Finish(1, 0);
        }

        private void button3_Click(object sender, EventArgs e)
        {
            // 创建OpenFileDialog实例
            OpenFileDialog openFileDialog = new OpenFileDialog();
            // 设置文件筛选器，只显示常见的图片格式文件
            openFileDialog.Filter = "图片文件(*.jpg;*.png;*.bmp;*.gif)|*.jpg;*.png;*.bmp;*.gif";
            // 设置初始目录为当前用户的图片文件夹（可根据实际需求更改）
            openFileDialog.InitialDirectory = "D:\\Project\\架构\\image_test\\bin\\x64\\Debug\\image\\support";
            // 允许选择多个文件（这里设置为false，只允许选择一个文件，如果需要多选可设为true）
            //openFileDialog.Multiple = false;

            // 显示对话框并判断用户是否点击了确定按钮
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                // 获取用户选择的文件路径
                string filePath = openFileDialog.FileName;
                CameraImageLib.SetPicPath(filePath);
                richTextBox1.Text = openFileDialog.FileName;
            }            
        }

        private void button4_Click(object sender, EventArgs e)
        {
            Finish(2, 0);
        }

        private void button5_Click(object sender, EventArgs e)
        {
            Finish(3, 0);
        }

        private void button6_Click(object sender, EventArgs e)
        {
            Finish(4, 0);
        }

        private void button7_Click(object sender, EventArgs e)
        {
            message_sem.Release();
        }

        private void button8_Click(object sender, EventArgs e)
        {
            Finish(5, 0);
        }
    }

    public class ImageRes
    {
        static string resourcePath1 = "image\\2025_2_11";
        static string resourcePath2 = "image\\2025_2_11";
        public static List<System.Drawing.Image> files1 = new List<System.Drawing.Image>();
        public static List<System.Drawing.Image> files2 = new List<System.Drawing.Image>();
        public static int totalImgs = 0;

        public static int maxImgs = 0;

        public static int getResource()
        {
            try
            {
                DirectoryInfo imgs = new DirectoryInfo(resourcePath1);
                foreach (FileInfo NextFile in imgs.GetFiles())
                {
                    string file = Path.Combine(resourcePath1, NextFile.Name);
                    Logs.WriteInfo(file, true);
                    files1.Add(System.Drawing.Image.FromFile(file));
                }

                imgs = new DirectoryInfo(resourcePath2);
                foreach (FileInfo NextFile in imgs.GetFiles())
                {
                    string file = Path.Combine(resourcePath2, NextFile.Name);
                    Logs.WriteInfo(file, true);
                    files2.Add(System.Drawing.Image.FromFile(file));
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + " need: Gamma_L1 Gamma_L2 ");
                Process.GetCurrentProcess().Kill();
            }

            return 0;
        }
    }
}
