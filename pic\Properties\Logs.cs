﻿using log4net.Appender;
using log4net;
using log4net;
using log4net.Appender;
using System;
using System.IO;

namespace Nreal_ProductLine_Tool.PBS
{
    public class Logs
    {
        //日志文件夹
        //private static bool debug_info = false;
        private static string filePath = AppDomain.CurrentDomain.BaseDirectory + @"Logs\";
        //指定的记录器
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger("mylog4");
        public Logs()
        {
            //使用指定的log4配置文件配置log4
            log4net.Config.XmlConfigurator.Configure(new FileInfo(@"log4net.config"));
            //查看文件夹是否存在，不存在就创建
            if (!Directory.Exists(filePath))
            {
                Directory.CreateDirectory(filePath);
            }
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="msg"></param>
        /// <param name=""></param>
        public static void WriteLog(string msg, bool isWrite, Action<object> action)
        {
            if (isWrite)
            {
                //日志名字
                string filename = DateTime.Now.ToString("yyyy年MM月dd日") + ".log";
                //默认的log4实例
                var repository = LogManager.GetRepository();
                #region 记录日志
                var appenders = repository.GetAppenders();
                if (appenders.Length > 0)
                {
                    RollingFileAppender targetApder = null;
                    foreach (var Apder in appenders)
                    {
                        if (Apder.Name == "mylog4")
                        {
                            targetApder = Apder as RollingFileAppender;
                            break;
                        }
                    }
                    if (targetApder.Name == "mylog4")//如果是文件输出类型日志，则更改输出路径
                    {
                        if (targetApder != null)
                        {
                            if (!targetApder.File.Contains(filename))
                            {
                                targetApder.File = @"Logs\" + filename;
                                targetApder.ActivateOptions();
                            }
                        }
                    }
                }
                #endregion
                action(msg);
            }
        }

        #region 类型
        /// <summary>
        /// 错误
        /// </summary>
        public static void WriteError(string msg, bool isWrite)
        {
            WriteLog(msg, isWrite, log.Error);
        }
        /// <summary>
        /// 信息
        /// </summary>
        public static void WriteInfo(string msg, bool isWrite)
        {
            WriteLog(msg, isWrite, log.Info);
        }
        /// <summary>
        /// 警告
        /// </summary>
        public static void WriteWarn(string msg, bool isWrite)
        {
            WriteLog(msg, isWrite, log.Warn);
        }

        public static void WriteDebug(string msg, bool isWrite)
        {
            WriteLog(msg, isWrite, log.Debug);
        }
        #endregion
    }
}
