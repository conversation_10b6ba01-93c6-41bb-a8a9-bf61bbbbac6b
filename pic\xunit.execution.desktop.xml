<?xml version="1.0"?>
<doc>
    <assembly>
        <name>xunit.execution.desktop</name>
    </assembly>
    <members>
        <member name="T:ReflectionAbstractionExtensions">
            <summary>
            This class represents utility methods needed to supplement the
            reflection capabilities provided by the CLR
            </summary>
        </member>
        <member name="M:ReflectionAbstractionExtensions.CreateTestClass(Xunit.Abstractions.ITest,System.Type,System.Object[],Xunit.Sdk.IMessageBus,Xunit.Sdk.ExecutionTimer,System.Threading.CancellationTokenSource)">
            <summary>
            Creates an instance of the test class for the given test case. Sends the <see cref="T:Xunit.Abstractions.ITestClassConstructionStarting"/>
            and <see cref="T:Xunit.Abstractions.ITestClassConstructionFinished"/> messages as appropriate.
            </summary>
            <param name="test">The test</param>
            <param name="testClassType">The type of the test class</param>
            <param name="constructorArguments">The constructor arguments for the test class</param>
            <param name="messageBus">The message bus used to send the test messages</param>
            <param name="timer">The timer used to measure the time taken for construction</param>
            <param name="cancellationTokenSource">The cancellation token source</param>
            <returns></returns>
        </member>
        <member name="M:ReflectionAbstractionExtensions.DisposeTestClass(Xunit.Abstractions.ITest,System.Object,Xunit.Sdk.IMessageBus,Xunit.Sdk.ExecutionTimer,System.Threading.CancellationTokenSource)">
            <summary>
            Disposes the test class instance. Sends the <see cref="T:Xunit.Abstractions.ITestClassDisposeStarting"/> and <see cref="T:Xunit.Abstractions.ITestClassDisposeFinished"/>
            messages as appropriate.
            </summary>
            <param name="test">The test</param>
            <param name="testClass">The test class instance to be disposed</param>
            <param name="messageBus">The message bus used to send the test messages</param>
            <param name="timer">The timer used to measure the time taken for construction</param>
            <param name="cancellationTokenSource">The cancellation token source</param>
        </member>
        <member name="M:ReflectionAbstractionExtensions.GetMatchingMethods(System.Type,System.Reflection.MethodInfo)">
            <summary>
            Gets methods in the target type that match the protection level of the supplied method
            </summary>
            <param name="type">The type</param>
            <param name="methodInfo">The method</param>
            <returns>The reflection method informations that match</returns>
        </member>
        <member name="M:ReflectionAbstractionExtensions.GetCustomAttributes(Xunit.Abstractions.IAssemblyInfo,System.Type)">
            <summary>
            Gets all the custom attributes for the given assembly.
            </summary>
            <param name="assemblyInfo">The assembly</param>
            <param name="attributeType">The type of the attribute</param>
            <returns>The matching attributes that decorate the assembly</returns>
        </member>
        <member name="M:ReflectionAbstractionExtensions.GetCustomAttributes(Xunit.Abstractions.IAttributeInfo,System.Type)">
            <summary>
            Gets all the custom attributes for the given attribute.
            </summary>
            <param name="attributeInfo">The attribute</param>
            <param name="attributeType">The type of the attribute to find</param>
            <returns>The matching attributes that decorate the attribute</returns>
        </member>
        <member name="M:ReflectionAbstractionExtensions.GetCustomAttributes(Xunit.Abstractions.IMethodInfo,System.Type)">
            <summary>
            Gets all the custom attributes for the method that are of the given type.
            </summary>
            <param name="methodInfo">The method</param>
            <param name="attributeType">The type of the attribute</param>
            <returns>The matching attributes that decorate the method</returns>
        </member>
        <member name="M:ReflectionAbstractionExtensions.GetCustomAttributes(Xunit.Abstractions.ITypeInfo,System.Type)">
            <summary>
            Gets all the custom attributes for the given type.
            </summary>
            <param name="typeInfo">The type</param>
            <param name="attributeType">The type of the attribute</param>
            <returns>The matching attributes that decorate the type</returns>
        </member>
        <member name="M:ReflectionAbstractionExtensions.ToRuntimeMethod(Xunit.Abstractions.IMethodInfo)">
            <summary>
            Converts an <see cref="T:Xunit.Abstractions.IMethodInfo"/> into a <see cref="T:System.Reflection.MethodInfo"/>, if possible (for example, this
            will not work when the test method is based on source code rather than binaries).
            </summary>
            <param name="methodInfo">The method to convert</param>
            <returns>The runtime method, if available; <c>null</c>, otherwise</returns>
        </member>
        <member name="M:ReflectionAbstractionExtensions.ToRuntimeType(Xunit.Abstractions.ITypeInfo)">
            <summary>
            Converts an <see cref="T:Xunit.Abstractions.ITypeInfo"/> into a <see cref="T:System.Type"/>, if possible (for example, this
            will not work when the test class is based on source code rather than binaries).
            </summary>
            <param name="typeInfo">The type to convert</param>
            <returns>The runtime type, if available, <c>null</c>, otherwise</returns>
        </member>
        <member name="T:TestFrameworkOptionsReadExtensions">
            <summary>
            Extension methods for reading <see cref="T:Xunit.Abstractions.ITestFrameworkDiscoveryOptions"/> and <see cref="T:Xunit.Abstractions.ITestFrameworkExecutionOptions"/>.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.DiagnosticMessages(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets a flag that determines whether diagnostic messages will be emitted.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.DiagnosticMessagesOrDefault(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets a flag that determines whether diagnostic messages will be emitted. If the flag is not present,
            returns the default value (<c>false</c>).
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.MethodDisplay(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets a flag that determines the default display name format for test methods.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.MethodDisplayOptions(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets a flag that determines the default display options to format test methods.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.MethodDisplayOrDefault(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets a flag that determines the default display name format for test methods. If the flag is not present,
            returns the default value (<see cref="F:Xunit.Sdk.TestMethodDisplay.ClassAndMethod"/>).
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.MethodDisplayOptionsOrDefault(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets the options that determine the default display formatting options for test methods. If no options are not present,
            returns the default value (<see cref="F:Xunit.Sdk.TestMethodDisplayOptions.None"/>).
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.PreEnumerateTheories(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets a flag that determines whether theories are pre-enumerated. If they enabled, then the
            discovery system will return a test case for each row of test data; they are disabled, then the
            discovery system will return a single test case for the theory.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.PreEnumerateTheoriesOrDefault(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets a flag that determines whether theories are pre-enumerated. If enabled, then the
            discovery system will return a test case for each row of test data; if disabled, then the
            discovery system will return a single test case for the theory. If the flag is not present,
            returns the default value (<c>true</c>).
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.SynchronousMessageReporting(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets a flag that determines whether xUnit.net should report test results synchronously.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.SynchronousMessageReportingOrDefault(Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Gets a flag that determines whether xUnit.net should report test results synchronously.
            If the flag is not set, returns the default value (<c>false</c>).
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.DiagnosticMessages(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets a flag that determines whether diagnostic messages will be emitted.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.DiagnosticMessagesOrDefault(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets a flag that determines whether diagnostic messages will be emitted. If the flag is not
            present, returns the default value (<c>false</c>).
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.DisableParallelization(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets a flag to disable parallelization.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.DisableParallelizationOrDefault(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets a flag to disable parallelization. If the flag is not present, returns the
            default value (<c>false</c>).
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.MaxParallelThreads(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets the maximum number of threads to use when running tests in parallel.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.MaxParallelThreadsOrDefault(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets the maximum number of threads to use when running tests in parallel. If set to 0 (or not set),
            the value of <see cref="P:System.Environment.ProcessorCount"/> is used; if set to a value less
            than 0, does not limit the number of threads.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.StopOnTestFail(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets a flag to stop testing on test failure.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.StopOnTestFailOrDefault(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets a flag to stop testing on test failure. If the flag is not present, returns the
            default value (<c>false</c>).
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.SynchronousMessageReporting(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets a flag that determines whether xUnit.net should report test results synchronously.
            </summary>
        </member>
        <member name="M:TestFrameworkOptionsReadExtensions.SynchronousMessageReportingOrDefault(Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Gets a flag that determines whether xUnit.net should report test results synchronously.
            If the flag is not set, returns the default value (<c>false</c>).
            </summary>
        </member>
        <member name="T:Xunit.Sdk.AsyncTestSyncContext">
            <summary>
            This implementation of <see cref="T:System.Threading.SynchronizationContext"/> allows the developer to track the count
            of outstanding "async void" operations, and wait for them all to complete.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.AsyncTestSyncContext.#ctor(System.Threading.SynchronizationContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.AsyncTestSyncContext"/> class.
            </summary>
            <param name="innerContext">The existing synchronization context (may be <c>null</c>).</param>
        </member>
        <member name="M:Xunit.Sdk.AsyncTestSyncContext.OperationCompleted">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AsyncTestSyncContext.OperationStarted">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AsyncTestSyncContext.Post(System.Threading.SendOrPostCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AsyncTestSyncContext.Send(System.Threading.SendOrPostCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AsyncTestSyncContext.WaitForCompletionAsync">
            <summary>
            Returns a task which is signaled when all outstanding operations are complete.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.DefaultTestCaseOrderer">
            <summary>
            Default implementation of <see cref="T:Xunit.Sdk.ITestCaseOrderer"/>. Orders tests in
            an unpredictable but stable order, so that repeated test runs of the
            identical test assembly run tests in the same order.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DefaultTestCaseOrderer.#ctor(Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DefaultTestCaseOrderer"/> class.
            </summary>
            <param name="diagnosticMessageSink">Message sink to report diagnostic messages to</param>
        </member>
        <member name="M:Xunit.Sdk.DefaultTestCaseOrderer.OrderTestCases``1(System.Collections.Generic.IEnumerable{``0})">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.DefaultTestCollectionOrderer">
            <summary>
            Default implementation of <see cref="T:Xunit.ITestCollectionOrderer"/>. Orders tests in
            an unpredictable and unstable order, so that repeated test runs of the
            identical test assembly run test collections in a random order.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DefaultTestCollectionOrderer.OrderTestCollections(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCollection})">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.DisposalTracker">
            <summary>
            Tracks disposable objects, and disposes them in the reverse order they were added to
            the tracker.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DisposalTracker.Add(System.IDisposable)">
            <summary>
            Add an object to be disposed.
            </summary>
            <param name="disposable">The object to be disposed.</param>
        </member>
        <member name="M:Xunit.Sdk.DisposalTracker.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.ExtensibilityPointFactory">
            <summary>
            Represents a caching factory for the types used for extensibility throughout the system.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.Dispose">
            <summary>
            Disposes the instances that are contained in the cache.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.Get``1(Xunit.Abstractions.IMessageSink,System.Type,System.Object[])">
            <summary>
            Gets an instance of the given type, casting it to <typeparamref name="TInterface"/>, using the provided
            constructor arguments. There is a single instance of a given type that is cached and reused,
            so classes retrieved from this factory must be stateless and thread-safe.
            </summary>
            <typeparam name="TInterface">The interface type.</typeparam>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="type">The implementation type.</param>
            <param name="ctorArgs">The constructor arguments. Since diagnostic message sinks are optional,
            the code first looks for a type that takes the given arguments plus the message sink, and only
            falls back to the message sink-less constructor if none was found.</param>
            <returns>The instance of the type.</returns>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetDataDiscoverer(Xunit.Abstractions.IMessageSink,System.Type)">
            <summary>
            Gets a data discoverer.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="discovererType">The discoverer type</param>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetDataDiscoverer(Xunit.Abstractions.IMessageSink,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Gets a data discoverer, as specified in a reflected <see cref="T:Xunit.Sdk.DataDiscovererAttribute"/>.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="dataDiscovererAttribute">The data discoverer attribute</param>
            <returns>The data discoverer, if the type is loadable; <c>null</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetTestCaseOrderer(Xunit.Abstractions.IMessageSink,System.Type)">
            <summary>
            Gets a test case orderer.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="ordererType">The test case orderer type</param>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetTestCaseOrderer(Xunit.Abstractions.IMessageSink,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Gets a test case orderer, as specified in a reflected <see cref="T:Xunit.TestCaseOrdererAttribute"/>.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="testCaseOrdererAttribute">The test case orderer attribute.</param>
            <returns>The test case orderer, if the type is loadable; <c>null</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetTestCollectionOrderer(Xunit.Abstractions.IMessageSink,System.Type)">
            <summary>
            Gets a test collection orderer.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="ordererType">The test collection orderer type</param>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetTestCollectionOrderer(Xunit.Abstractions.IMessageSink,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Gets a test collection orderer, as specified in a reflected <see cref="T:Xunit.TestCollectionOrdererAttribute"/>.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="testCollectionOrdererAttribute">The test collection orderer attribute.</param>
            <returns>The test collection orderer, if the type is loadable; <c>null</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetTestFrameworkTypeDiscoverer(Xunit.Abstractions.IMessageSink,System.Type)">
            <summary>
            Gets a test framework discoverer.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="frameworkType">The test framework type discoverer type</param>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetTestFrameworkTypeDiscoverer(Xunit.Abstractions.IMessageSink,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Gets a test framework discoverer, as specified in a reflected <see cref="T:Xunit.Sdk.TestFrameworkDiscovererAttribute"/>.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="testFrameworkDiscovererAttribute">The test framework discoverer attribute</param>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetTraitDiscoverer(Xunit.Abstractions.IMessageSink,System.Type)">
            <summary>
            Gets a trait discoverer.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="traitDiscovererType">The trait discoverer type</param>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetTraitDiscoverer(Xunit.Abstractions.IMessageSink,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Gets a trait discoverer, as specified in a reflected <see cref="T:Xunit.Sdk.TraitDiscovererAttribute"/>.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="traitDiscovererAttribute">The trait discoverer attribute.</param>
            <returns>The trait discoverer, if the type is loadable; <c>null</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetXunitTestCaseDiscoverer(Xunit.Abstractions.IMessageSink,System.Type)">
            <summary>
            Gets an xUnit.net v2 test discoverer.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="testCaseDiscovererType">The test case discoverer type</param>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetXunitTestCollectionFactory(Xunit.Abstractions.IMessageSink,System.Type,Xunit.Abstractions.ITestAssembly)">
            <summary>
            Gets an xUnit.net v2 test collection factory.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="testCollectionFactoryType">The test collection factory type</param>
            <param name="testAssembly">The test assembly under test</param>
        </member>
        <member name="M:Xunit.Sdk.ExtensibilityPointFactory.GetXunitTestCollectionFactory(Xunit.Abstractions.IMessageSink,Xunit.Abstractions.IAttributeInfo,Xunit.Abstractions.ITestAssembly)">
            <summary>
            Gets an xUnit.net v2 test collection factory, as specified in a reflected <see cref="T:Xunit.CollectionBehaviorAttribute"/>.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="collectionBehaviorAttribute">The collection behavior attribute.</param>
            <param name="testAssembly">The test assembly.</param>
            <returns>The collection factory.</returns>
        </member>
        <member name="T:Xunit.Sdk.CollectionPerAssemblyTestCollectionFactory">
            <summary>
            Implementation of <see cref="T:Xunit.Sdk.IXunitTestCollectionFactory"/> that creates a single
            default test collection for the assembly, and places any tests classes without
            the <see cref="T:Xunit.CollectionAttribute"/> into it.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.CollectionPerAssemblyTestCollectionFactory.#ctor(Xunit.Abstractions.ITestAssembly,Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.CollectionPerAssemblyTestCollectionFactory" /> class.
            </summary>
            <param name="testAssembly">The assembly.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
        </member>
        <member name="P:Xunit.Sdk.CollectionPerAssemblyTestCollectionFactory.DisplayName">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionPerAssemblyTestCollectionFactory.Get(Xunit.Abstractions.ITypeInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.CollectionPerClassTestCollectionFactory">
            <summary>
            Implementation of <see cref="T:Xunit.Sdk.IXunitTestCollectionFactory"/> which creates a new test
            collection for each test class that isn't decorated with <see cref="T:Xunit.CollectionAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.CollectionPerClassTestCollectionFactory.#ctor(Xunit.Abstractions.ITestAssembly,Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.CollectionPerClassTestCollectionFactory" /> class.
            </summary>
            <param name="testAssembly">The assembly info.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
        </member>
        <member name="P:Xunit.Sdk.CollectionPerClassTestCollectionFactory.DisplayName">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.CollectionPerClassTestCollectionFactory.Get(Xunit.Abstractions.ITypeInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.DisplayNameFormatter">
            <summary>
            Represents a formatter that formats the display name of a class and/or method into a more
            human readable form using additional options.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DisplayNameFormatter.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DisplayNameFormatter"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DisplayNameFormatter.#ctor(Xunit.Sdk.TestMethodDisplay,Xunit.Sdk.TestMethodDisplayOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DisplayNameFormatter"/> class.
            </summary>
            <param name="display">The <see cref="T:Xunit.Sdk.TestMethodDisplay"/> used by the formatter.</param>
            <param name="displayOptions">The <see cref="T:Xunit.Sdk.TestMethodDisplayOptions"/> used by the formatter.</param>
        </member>
        <member name="M:Xunit.Sdk.DisplayNameFormatter.Format(System.String)">
            <summary>
            Formats the specified display name.
            </summary>
            <param name="displayName">The display name to format.</param>
            <returns>The formatted display name.</returns>
        </member>
        <member name="T:Xunit.Sdk.ExecutionErrorTestCase">
            <summary>
            A simple implementation of <see cref="T:Xunit.Sdk.IXunitTestCase"/> that can be used to report an error
            rather than running a test.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExecutionErrorTestCase.#ctor">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.ExecutionErrorTestCase.#ctor(Xunit.Abstractions.IMessageSink,Xunit.Sdk.TestMethodDisplay,Xunit.Abstractions.ITestMethod,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ExecutionErrorTestCase"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="testMethod">The test method.</param>
            <param name="errorMessage">The error message to report for the test.</param>
        </member>
        <member name="M:Xunit.Sdk.ExecutionErrorTestCase.#ctor(Xunit.Abstractions.IMessageSink,Xunit.Sdk.TestMethodDisplay,Xunit.Sdk.TestMethodDisplayOptions,Xunit.Abstractions.ITestMethod,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ExecutionErrorTestCase"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="defaultMethodDisplayOptions">Default method display options to use (when not customized).</param>
            <param name="testMethod">The test method.</param>
            <param name="errorMessage">The error message to report for the test.</param>
        </member>
        <member name="P:Xunit.Sdk.ExecutionErrorTestCase.ErrorMessage">
            <summary>
            Gets the error message that will be display when the test is run.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExecutionErrorTestCase.RunAsync(Xunit.Abstractions.IMessageSink,Xunit.Sdk.IMessageBus,System.Object[],Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ExecutionErrorTestCase.Serialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ExecutionErrorTestCase.Deserialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.ExecutionTimer">
            <summary>
            Measures and aggregates execution time of one or more actions.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.ExecutionTimer.Total">
            <summary>
            Returns the total time aggregated across all the actions.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExecutionTimer.Aggregate(System.Action)">
            <summary>
            Executes an action and aggregates its run time into the total.
            </summary>
            <param name="action">The action to measure.</param>
        </member>
        <member name="M:Xunit.Sdk.ExecutionTimer.AggregateAsync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Executes an asynchronous action and aggregates its run time into the total.
            </summary>
            <param name="asyncAction">The action to measure.</param>
        </member>
        <member name="M:Xunit.Sdk.ExecutionTimer.Aggregate(System.TimeSpan)">
            <summary>
            Aggregates a time span into the total time.
            </summary>
            <param name="time">The time to add.</param>
        </member>
        <member name="T:Xunit.Sdk.FactDiscoverer">
            <summary>
            Implementation of <see cref="T:Xunit.Sdk.IXunitTestCaseDiscoverer"/> that supports finding test cases
            on methods decorated with <see cref="T:Xunit.FactAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.FactDiscoverer.#ctor(Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.FactDiscoverer"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
        </member>
        <member name="P:Xunit.Sdk.FactDiscoverer.DiagnosticMessageSink">
            <summary>
            Gets the message sink used to report <see cref="T:Xunit.Abstractions.IDiagnosticMessage"/> messages.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.FactDiscoverer.CreateTestCase(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Creates a single <see cref="T:Xunit.Sdk.XunitTestCase"/> for the given test method.
            </summary>
            <param name="discoveryOptions">The discovery options to be used.</param>
            <param name="testMethod">The test method.</param>
            <param name="factAttribute">The attribute that decorates the test method.</param>
            <returns></returns>
        </member>
        <member name="M:Xunit.Sdk.FactDiscoverer.Discover(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Discover test cases from a test method. By default, if the method is generic, or
            it contains arguments, returns a single <see cref="T:Xunit.Sdk.ExecutionErrorTestCase"/>;
            otherwise, it returns the result of calling <see cref="M:Xunit.Sdk.FactDiscoverer.CreateTestCase(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo)"/>.
            </summary>
            <param name="discoveryOptions">The discovery options to be used.</param>
            <param name="testMethod">The test method the test cases belong to.</param>
            <param name="factAttribute">The fact attribute attached to the test method.</param>
            <returns>Returns zero or more test cases represented by the test method.</returns>
        </member>
        <member name="T:Xunit.Sdk.ExecutionErrorTestCaseRunner">
            <summary>
            An implementation of <see cref="T:Xunit.Sdk.TestCaseRunner`1"/> to support <see cref="T:Xunit.Sdk.ExecutionErrorTestCase"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExecutionErrorTestCaseRunner.#ctor(Xunit.Sdk.ExecutionErrorTestCase,Xunit.Sdk.IMessageBus,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ExecutionErrorTestCaseRunner"/> class.
            </summary>
            <param name="testCase">The test case that the lambda represents.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="M:Xunit.Sdk.ExecutionErrorTestCaseRunner.RunTestAsync">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestAssemblyRunner`1">
            <summary>
            A base class that provides default behavior when running tests in an assembly. It groups the tests
            by test collection, and then runs the individual test collections.
            </summary>
            <typeparam name="TTestCase">The type of the test case used by the test framework. Must
            derive from <see cref="T:Xunit.Abstractions.ITestCase"/>.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.#ctor(Xunit.Abstractions.ITestAssembly,System.Collections.Generic.IEnumerable{`0},Xunit.Abstractions.IMessageSink,Xunit.Abstractions.IMessageSink,Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestAssemblyRunner`1"/> class.
            </summary>
            <param name="testAssembly">The assembly that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="diagnosticMessageSink">The message sink to report diagnostic messages to.</param>
            <param name="executionMessageSink">The message sink to report run status to.</param>
            <param name="executionOptions">The user's requested execution options.</param>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyRunner`1.Aggregator">
            <summary>
            Gets or sets the exception aggregator used to run code and collect exceptions.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyRunner`1.ExecutionOptions">
            <summary>
            Gets or sets the user's requested execution options.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyRunner`1.DiagnosticMessageSink">
            <summary>
            Gets or sets the message sink to report diagnostic messages to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyRunner`1.ExecutionMessageSink">
            <summary>
            Gets or sets the message sink to report run status to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyRunner`1.TestAssembly">
            <summary>
            Gets or sets the assembly that contains the tests to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyRunner`1.TestCaseOrderer">
            <summary>
            Gets or sets the test case orderer that will be used to decide how to order the tests.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyRunner`1.TestCollectionOrderer">
            <summary>
            Gets or sets the test collection orderer that will be used to decide how to order the test collections.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyRunner`1.TestCases">
            <summary>
            Gets or sets the test cases to be run.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.GetTestFrameworkDisplayName">
            <summary>
            Override this to provide the display name for the test framework (f.e., "xUnit.net 2.0").
            This value is placed into <see cref="P:Xunit.Abstractions.ITestAssemblyStarting.TestFrameworkDisplayName"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.GetTestFrameworkEnvironment">
            <summary>
            Override this to provide the environment information (f.e., "32-bit .NET 4.0"). This value is
            placed into <see cref="P:Xunit.Abstractions.ITestAssemblyStarting.TestEnvironment"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.AfterTestAssemblyStartingAsync">
            <summary>
            This method is called just after <see cref="T:Xunit.Abstractions.ITestAssemblyStarting"/> is sent, but before any test collections are run.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestAssemblyRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.BeforeTestAssemblyFinishedAsync">
            <summary>
            This method is called just before <see cref="T:Xunit.Abstractions.ITestAssemblyFinished"/> is sent.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestAssemblyRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.CreateMessageBus">
            <summary>
            Creates the message bus to be used for test execution. By default, it inspects
            the options for the <see cref="F:TestOptionsNames.Execution.SynchronousMessageReporting"/>
            flag, and if present, creates a message bus that ensures all messages are delivered
            on the same thread.
            </summary>
            <returns>The message bus.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.OrderTestCollections">
            <summary>
            Orders the test collections using the <see cref="P:Xunit.Sdk.TestAssemblyRunner`1.TestCollectionOrderer"/>.
            </summary>
            <returns>Test collections (and the associated test cases) in run order</returns>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.RunAsync">
            <summary>
            Runs the tests in the test assembly.
            </summary>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.RunTestCollectionsAsync(Xunit.Sdk.IMessageBus,System.Threading.CancellationTokenSource)">
            <summary>
            Runs the list of test collections. By default, groups the tests by collection and runs them synchronously.
            </summary>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyRunner`1.RunTestCollectionAsync(Xunit.Sdk.IMessageBus,Xunit.Abstractions.ITestCollection,System.Collections.Generic.IEnumerable{`0},System.Threading.CancellationTokenSource)">
            <summary>
            Override this method to run the tests in an individual test collection.
            </summary>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="testCollection">The test collection that is being run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="T:Xunit.Sdk.TestCaseRunner`1">
            <summary>
            A base class that provides default behavior when running test cases.
            </summary>
            <typeparam name="TTestCase">The type of the test case used by the test framework. Must
            derive from <see cref="T:Xunit.Abstractions.ITestCase"/>.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.TestCaseRunner`1.#ctor(`0,Xunit.Sdk.IMessageBus,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCaseRunner`1"/> class.
            </summary>
            <param name="testCase">The test case to be run.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.TestCaseRunner`1.Aggregator">
            <summary>
            Gets or sets the exception aggregator used to run code and collect exceptions.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCaseRunner`1.CancellationTokenSource">
            <summary>
            Gets or sets the task cancellation token source, used to cancel the test run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCaseRunner`1.MessageBus">
            <summary>
            Gets or sets the message bus to report run status to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCaseRunner`1.TestCase">
            <summary>
            Gets or sets the test case to be run.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseRunner`1.AfterTestCaseStartingAsync">
            <summary>
            This method is called just after <see cref="T:Xunit.Abstractions.ITestCaseStarting"/> is sent, but before any test collections are run.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestCaseRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseRunner`1.BeforeTestCaseFinishedAsync">
            <summary>
            This method is called just before <see cref="T:Xunit.Abstractions.ITestCaseFinished"/> is sent.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestCaseRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseRunner`1.RunAsync">
            <summary>
            Runs the tests in the test case.
            </summary>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestCaseRunner`1.RunTestAsync">
            <summary>
            Override this method to run the tests in an individual test method.
            </summary>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="T:Xunit.Sdk.TestClassRunner`1">
            <summary>
            A base class that provides default behavior when running tests in a test class. It groups the tests
            by test method, and then runs the individual test methods.
            </summary>
            <typeparam name="TTestCase">The type of the test case used by the test framework. Must
            derive from <see cref="T:Xunit.Abstractions.ITestCase"/>.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.#ctor(Xunit.Abstractions.ITestClass,Xunit.Abstractions.IReflectionTypeInfo,System.Collections.Generic.IEnumerable{`0},Xunit.Abstractions.IMessageSink,Xunit.Sdk.IMessageBus,Xunit.Sdk.ITestCaseOrderer,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassRunner`1"/> class.
            </summary>
            <param name="testClass">The test class to be run.</param>
            <param name="class">The test class that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="testCaseOrderer">The test case orderer that will be used to decide how to order the test.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.TestClassRunner`1.Aggregator">
            <summary>
            Gets or sets the exception aggregator used to run code and collect exceptions.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassRunner`1.CancellationTokenSource">
            <summary>
            Gets or sets the task cancellation token source, used to cancel the test run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassRunner`1.Class">
            <summary>
            Gets or sets the CLR class that contains the tests to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassRunner`1.DiagnosticMessageSink">
            <summary>
            Gets the message sink used to send diagnostic messages.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassRunner`1.MessageBus">
            <summary>
            Gets or sets the message bus to report run status to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassRunner`1.TestCaseOrderer">
            <summary>
            Gets or sets the test case orderer that will be used to decide how to order the test.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassRunner`1.TestCases">
            <summary>
            Gets or sets the test cases to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassRunner`1.TestClass">
            <summary>
            Gets or sets the test class to be run.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.CreateTestClassConstructorArguments">
            <summary>
            Creates the arguments for the test class constructor. Attempts to resolve each parameter
            individually, and adds an error when the constructor arguments cannot all be provided.
            If the class is static, does not look for constructor, since one will not be needed.
            </summary>
            <returns>The test class constructor arguments.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.FormatConstructorArgsMissingMessage(System.Reflection.ConstructorInfo,System.Collections.Generic.IReadOnlyList{System.Tuple{System.Int32,System.Reflection.ParameterInfo}})">
            <summary>
            Gets the message to be used when the constructor is missing arguments.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.AfterTestClassStartingAsync">
            <summary>
            This method is called just after <see cref="T:Xunit.Abstractions.ITestClassStarting"/> is sent, but before any test methods are run.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestClassRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.BeforeTestClassFinishedAsync">
            <summary>
            This method is called just before <see cref="T:Xunit.Abstractions.ITestClassFinished"/> is sent.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestClassRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.RunAsync">
            <summary>
            Runs the tests in the test class.
            </summary>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.RunTestMethodsAsync">
            <summary>
            Runs the list of test methods. By default, orders the tests, groups them by method and runs them synchronously.
            </summary>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.RunTestMethodAsync(Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IReflectionMethodInfo,System.Collections.Generic.IEnumerable{`0},System.Object[])">
            <summary>
            Override this method to run the tests in an individual test method.
            </summary>
            <param name="testMethod">The test method that contains the test cases.</param>
            <param name="method">The CLR method that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="constructorArguments">The constructor arguments that will be used to create the test class.</param>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.SelectTestClassConstructor">
            <summary>
            Selects the constructor to be used for the test class. By default, chooses the parameterless
            constructor. Override to change the constructor selection logic.
            </summary>
            <returns>The constructor to be used for creating the test class.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestClassRunner`1.TryGetConstructorArgument(System.Reflection.ConstructorInfo,System.Int32,System.Reflection.ParameterInfo,System.Object@)">
            <summary>
            Tries to supply a test class constructor argument. By default, always fails. Override to
            change the argument lookup logic.
            </summary>
            <param name="constructor">The constructor that will be used to create the test class.</param>
            <param name="index">The parameter index.</param>
            <param name="parameter">The parameter information.</param>
            <param name="argumentValue">The argument value that should be used for the parameter.</param>
            <returns>Returns <c>true</c> if the argument was supplied; <c>false</c>, otherwise.</returns>
        </member>
        <member name="T:Xunit.Sdk.TestCollectionRunner`1">
            <summary>
            A base class that provides default behavior when running tests in a test collection. It groups the tests
            by test class, and then runs the individual test classes.
            </summary>
            <typeparam name="TTestCase">The type of the test case used by the test framework. Must
            derive from <see cref="T:Xunit.Abstractions.ITestCase"/>.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionRunner`1.#ctor(Xunit.Abstractions.ITestCollection,System.Collections.Generic.IEnumerable{`0},Xunit.Sdk.IMessageBus,Xunit.Sdk.ITestCaseOrderer,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCollectionRunner`1"/> class.
            </summary>
            <param name="testCollection">The test collection that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="testCaseOrderer">The test case orderer that will be used to decide how to order the test.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionRunner`1.Aggregator">
            <summary>
            Gets or sets the exception aggregator used to run code and collect exceptions.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionRunner`1.CancellationTokenSource">
            <summary>
            Gets or sets the task cancellation token source, used to cancel the test run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionRunner`1.MessageBus">
            <summary>
            Gets or sets the message bus to report run status to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionRunner`1.TestCaseOrderer">
            <summary>
            Gets or sets the test case orderer that will be used to decide how to order the test.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionRunner`1.TestCases">
            <summary>
            Gets or sets the test cases to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionRunner`1.TestCollection">
            <summary>
            Gets or sets the test collection that contains the tests to be run.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionRunner`1.AfterTestCollectionStartingAsync">
            <summary>
            This method is called just after <see cref="T:Xunit.Abstractions.ITestCollectionStarting"/> is sent, but before any test classes are run.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestCollectionRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionRunner`1.BeforeTestCollectionFinishedAsync">
            <summary>
            This method is called just before <see cref="T:Xunit.Abstractions.ITestCollectionFinished"/> is sent.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestCollectionRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionRunner`1.RunAsync">
            <summary>
            Runs the tests in the test collection.
            </summary>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionRunner`1.RunTestClassesAsync">
            <summary>
            Runs the list of test classes. By default, groups the tests by class and runs them synchronously.
            </summary>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionRunner`1.RunTestClassAsync(Xunit.Abstractions.ITestClass,Xunit.Abstractions.IReflectionTypeInfo,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Override this method to run the tests in an individual test class.
            </summary>
            <param name="testClass">The test class to be run.</param>
            <param name="class">The CLR class that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="T:Xunit.Sdk.TestInvoker`1">
            <summary>
            A base class that provides default behavior to invoke a test method. This includes
            support for async test methods (both "async Task" and "async void") as well as
            creation and disposal of the test class.
            </summary>
            <typeparam name="TTestCase">The type of the test case used by the test framework. Must
            derive from <see cref="T:Xunit.Abstractions.ITestCase"/>.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.TestInvoker`1.#ctor(Xunit.Abstractions.ITest,Xunit.Sdk.IMessageBus,System.Type,System.Object[],System.Reflection.MethodInfo,System.Object[],Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestInvoker`1"/> class.
            </summary>
            <param name="test">The test that this invocation belongs to.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="testClass">The test class that the test method belongs to.</param>
            <param name="constructorArguments">The arguments to be passed to the test class constructor.</param>
            <param name="testMethod">The test method that will be invoked.</param>
            <param name="testMethodArguments">The arguments to be passed to the test method.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.Aggregator">
            <summary>
            Gets or sets the exception aggregator used to run code and collect exceptions.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.CancellationTokenSource">
            <summary>
            Gets or sets the task cancellation token source, used to cancel the test run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.ConstructorArguments">
            <summary>
            Gets or sets the constructor arguments used to construct the test class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.DisplayName">
            <summary>
            Gets the display name of the invoked test.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.MessageBus">
            <summary>
            Gets or sets the message bus to report run status to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.Test">
            <summary>
            Gets or sets the test to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.TestCase">
            <summary>
            Gets the test case to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.TestClass">
            <summary>
            Gets or sets the runtime type of the class that contains the test method.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.TestMethod">
            <summary>
            Gets or sets the runtime method of the method that contains the test.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.TestMethodArguments">
            <summary>
            Gets or sets the arguments to pass to the test method when it's being invoked.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestInvoker`1.Timer">
            <summary>
            Gets or sets the object which measures execution time.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestInvoker`1.CreateTestClass">
            <summary>
            Creates the test class, unless the test method is static or there have already been errors. Note that
            this method times the creation of the test class (using <see cref="P:Xunit.Sdk.TestInvoker`1.Timer"/>). It is also responsible for
            sending the <see cref="T:Xunit.Abstractions.ITestClassConstructionStarting"/>and <see cref="T:Xunit.Abstractions.ITestClassConstructionFinished"/>
            messages, so if you override this method without calling the base, you are responsible for all of this behavior.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestInvoker`1.Aggregator"/>.
            </summary>
            <returns>The class instance, if appropriate; <c>null</c>, otherwise</returns>
        </member>
        <member name="M:Xunit.Sdk.TestInvoker`1.AfterTestMethodInvokedAsync">
            <summary>
            This method is called just after the test method has finished executing.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestInvoker`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestInvoker`1.BeforeTestMethodInvokedAsync">
            <summary>
            This method is called just before the test method is invoked.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestInvoker`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestInvoker`1.CallTestMethod(System.Object)">
            <summary>
            This method calls the test method via reflection. This is an available override point
            if you need to do some other form of invocation of the actual test method.
            </summary>
            <param name="testClassInstance">The instance of the test class</param>
            <returns>The return value from the test method invocation</returns>
        </member>
        <member name="M:Xunit.Sdk.TestInvoker`1.GetTaskFromResult(System.Object)">
            <summary>
            Given an object, will determine if it is an instance of <see cref="T:System.Threading.Tasks.Task"/> (in which case, it is
            directly returned), or an instance of <see cref="T:Microsoft.FSharp.Control.FSharpAsync`1"/>
            (in which case it is converted), or neither (in which case <c>null</c> is returned).
            </summary>
            <param name="obj">The object to convert</param>
        </member>
        <member name="M:Xunit.Sdk.TestInvoker`1.RunAsync">
            <summary>
            Creates the test class (if necessary), and invokes the test method.
            </summary>
            <returns>Returns the time (in seconds) spent creating the test class, running
            the test, and disposing of the test class.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestInvoker`1.InvokeTestMethodAsync(System.Object)">
            <summary>
            Invokes the test method on the given test class instance. This method sets up support for "async void"
            test methods, ensures that the test method has the correct number of arguments, then calls <see cref="M:Xunit.Sdk.TestInvoker`1.CallTestMethod(System.Object)"/>
            to do the actual method invocation. It ensure that any async test method is fully completed before returning, and
            returns the measured clock time that the invocation took.
            </summary>
            <param name="testClassInstance">The test class instance</param>
            <returns>Returns the time taken to invoke the test method</returns>
        </member>
        <member name="T:Xunit.Sdk.TestMethodRunner`1">
            <summary>
            A base class that provides default behavior when running tests in a test method.
            </summary>
            <typeparam name="TTestCase">The type of the test case used by the test framework. Must
            derive from <see cref="T:Xunit.Abstractions.ITestCase"/>.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.TestMethodRunner`1.#ctor(Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IReflectionTypeInfo,Xunit.Abstractions.IReflectionMethodInfo,System.Collections.Generic.IEnumerable{`0},Xunit.Sdk.IMessageBus,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestMethodRunner`1"/> class.
            </summary>
            <param name="testMethod">The test method under test.</param>
            <param name="class">The CLR class that contains the test method.</param>
            <param name="method">The CLR method that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.TestMethodRunner`1.Aggregator">
            <summary>
            Gets or sets the exception aggregator used to run code and collect exceptions.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodRunner`1.CancellationTokenSource">
            <summary>
            Gets or sets the task cancellation token source, used to cancel the test run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodRunner`1.Class">
            <summary>
            Gets or sets the CLR class that contains the test method.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodRunner`1.MessageBus">
            <summary>
            Gets or sets the message bus to report run status to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodRunner`1.Method">
            <summary>
            Gets or sets the CLR method that contains the tests to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodRunner`1.TestCases">
            <summary>
            Gets or sets the test cases to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodRunner`1.TestMethod">
            <summary>
            Gets or sets the test method that contains the test cases.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodRunner`1.AfterTestMethodStarting">
            <summary>
            This method is called just after <see cref="T:Xunit.Abstractions.ITestMethodStarting"/> is sent, but before any test cases are run.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestMethodRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodRunner`1.BeforeTestMethodFinished">
            <summary>
            This method is called just before <see cref="T:Xunit.Abstractions.ITestMethodFinished"/> is sent.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestMethodRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodRunner`1.RunAsync">
            <summary>
            Runs the tests in the test method.
            </summary>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestMethodRunner`1.RunTestCasesAsync">
            <summary>
            Runs the list of test cases. By default, it runs the cases in order, synchronously.
            </summary>
            <returns>Returns summary information about the tests that were run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestMethodRunner`1.RunTestCaseAsync(`0)">
            <summary>
            Override this method to run an individual test case.
            </summary>
            <param name="testCase">The test case to be run.</param>
            <returns>Returns summary information about the test case run.</returns>
        </member>
        <member name="T:Xunit.Sdk.TestRunner`1">
            <summary>
            A base class that provides default behavior when running a test. This includes support
            for skipping tests.
            </summary>
            <typeparam name="TTestCase">The type of the test case used by the test framework. Must
            derive from <see cref="T:Xunit.Abstractions.ITestCase"/>.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.TestRunner`1.#ctor(Xunit.Abstractions.ITest,Xunit.Sdk.IMessageBus,System.Type,System.Object[],System.Reflection.MethodInfo,System.Object[],System.String,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestRunner`1"/> class.
            </summary>
            <param name="test">The test that this invocation belongs to.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="testClass">The test class that the test method belongs to.</param>
            <param name="constructorArguments">The arguments to be passed to the test class constructor.</param>
            <param name="testMethod">The test method that will be invoked.</param>
            <param name="testMethodArguments">The arguments to be passed to the test method.</param>
            <param name="skipReason">The skip reason, if the test is to be skipped.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.Aggregator">
            <summary>
            Gets or sets the exception aggregator used to run code and collect exceptions.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.CancellationTokenSource">
            <summary>
            Gets or sets the task cancellation token source, used to cancel the test run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.ConstructorArguments">
            <summary>
            Gets or sets the constructor arguments used to construct the test class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.DisplayName">
            <summary>
            Gets or sets the display name of the invoked test.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.MessageBus">
            <summary>
            Gets or sets the message bus to report run status to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.SkipReason">
            <summary>
            Gets or sets the skip reason for the test, if set.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.Test">
            <summary>
            Gets or sets the test to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.TestCase">
            <summary>
            Gets the test case to be run.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.TestClass">
            <summary>
            Gets or sets the runtime type of the class that contains the test method.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.TestMethod">
            <summary>
            Gets or sets the runtime method of the method that contains the test.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestRunner`1.TestMethodArguments">
            <summary>
            Gets or sets the arguments to pass to the test method when it's being invoked.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestRunner`1.AfterTestStarting">
            <summary>
            This method is called just after <see cref="T:Xunit.Abstractions.ITestStarting"/> is sent, but before the test class is created.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestRunner`1.BeforeTestFinished">
            <summary>
            This method is called just before <see cref="T:Xunit.Abstractions.ITestFinished"/> is sent.
            This method should NEVER throw; any exceptions should be placed into the <see cref="P:Xunit.Sdk.TestRunner`1.Aggregator"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestRunner`1.RunAsync">
            <summary>
            Runs the test.
            </summary>
            <returns>Returns summary information about the test that was run.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestRunner`1.InvokeTestAsync(Xunit.Sdk.ExceptionAggregator)">
            <summary>
            Override this method to invoke the test.
            </summary>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <returns>Returns a tuple which includes the execution time (in seconds) spent running the
            test method, and any output that was returned by the test.</returns>
        </member>
        <member name="T:Xunit.Sdk.XunitTestAssemblyRunner">
            <summary>
            The test assembly runner for xUnit.net v2 tests.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.#ctor(Xunit.Abstractions.ITestAssembly,System.Collections.Generic.IEnumerable{Xunit.Sdk.IXunitTestCase},Xunit.Abstractions.IMessageSink,Xunit.Abstractions.IMessageSink,Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestAssemblyRunner"/> class.
            </summary>
            <param name="testAssembly">The assembly that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="diagnosticMessageSink">The message sink to report diagnostic messages to.</param>
            <param name="executionMessageSink">The message sink to report run status to.</param>
            <param name="executionOptions">The user's requested execution options.</param>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.GetTestFrameworkDisplayName">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.GetTestFrameworkEnvironment">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.SetupSyncContext(System.Int32)">
            <summary>
            Gets the synchronization context used when potentially running tests in parallel.
            If <paramref name="maxParallelThreads"/> is greater than 0, it creates
            and uses an instance of <see cref="T:Xunit.Sdk.MaxConcurrencySyncContext"/>.
            </summary>
            <param name="maxParallelThreads">The maximum number of parallel threads.</param>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.Initialize">
            <summary>
            Ensures the assembly runner is initialized (sets up the collection behavior,
            parallelization options, and test orderers from their assembly attributes).
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.AfterTestAssemblyStartingAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.BeforeTestAssemblyFinishedAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.RunTestCollectionsAsync(Xunit.Sdk.IMessageBus,System.Threading.CancellationTokenSource)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestAssemblyRunner.RunTestCollectionAsync(Xunit.Sdk.IMessageBus,Xunit.Abstractions.ITestCollection,System.Collections.Generic.IEnumerable{Xunit.Sdk.IXunitTestCase},System.Threading.CancellationTokenSource)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestCaseRunner">
            <summary>
            The test case runner for xUnit.net v2 tests.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCaseRunner.#ctor(Xunit.Sdk.IXunitTestCase,System.String,System.String,System.Object[],System.Object[],Xunit.Sdk.IMessageBus,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestCaseRunner"/> class.
            </summary>
            <param name="testCase">The test case to be run.</param>
            <param name="displayName">The display name of the test case.</param>
            <param name="skipReason">The skip reason, if the test is to be skipped.</param>
            <param name="constructorArguments">The arguments to be passed to the test class constructor.</param>
            <param name="testMethodArguments">The arguments to be passed to the test method.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCaseRunner.BeforeAfterAttributes">
            <summary>
            Gets the list of <see cref="T:Xunit.Sdk.BeforeAfterTestAttribute"/>s that will be used for this test case.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCaseRunner.ConstructorArguments">
            <summary>
            Gets or sets the arguments passed to the test class constructor
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCaseRunner.DisplayName">
            <summary>
            Gets or sets the display name of the test case
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCaseRunner.SkipReason">
            <summary>
            Gets or sets the skip reason for the test, if set.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCaseRunner.TestClass">
            <summary>
            Gets or sets the runtime type for the test class that the test method belongs to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCaseRunner.TestMethod">
            <summary>
            Gets of sets the runtime method for the test method that the test case belongs to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCaseRunner.TestMethodArguments">
            <summary>
            Gets or sets the arguments to pass to the test method when it's being invoked.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCaseRunner.CreateTest(Xunit.Sdk.IXunitTestCase,System.String)">
            <summary>
            Creates the <see cref="T:Xunit.Abstractions.ITest"/> instance for the given test case.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCaseRunner.CreateTestRunner(Xunit.Abstractions.ITest,Xunit.Sdk.IMessageBus,System.Type,System.Object[],System.Reflection.MethodInfo,System.Object[],System.String,System.Collections.Generic.IReadOnlyList{Xunit.Sdk.BeforeAfterTestAttribute},Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Creates the test runner used to run the given test.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCaseRunner.GetBeforeAfterTestAttributes">
            <summary>
            Gets the list of <see cref="T:Xunit.Sdk.BeforeAfterTestAttribute"/> attributes that apply to this test case.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCaseRunner.RunTestAsync">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestClassRunner">
            <summary>
            The test class runner for xUnit.net v2 tests.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestClassRunner.#ctor(Xunit.Abstractions.ITestClass,Xunit.Abstractions.IReflectionTypeInfo,System.Collections.Generic.IEnumerable{Xunit.Sdk.IXunitTestCase},Xunit.Abstractions.IMessageSink,Xunit.Sdk.IMessageBus,Xunit.Sdk.ITestCaseOrderer,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource,System.Collections.Generic.IDictionary{System.Type,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestClassRunner"/> class.
            </summary>
            <param name="testClass">The test class to be run.</param>
            <param name="class">The test class that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="testCaseOrderer">The test case orderer that will be used to decide how to order the test.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
            <param name="collectionFixtureMappings">The mapping of collection fixture types to fixtures.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTestClassRunner.ClassFixtureMappings">
            <summary>
            Gets the fixture mappings that were created during <see cref="M:Xunit.Sdk.XunitTestClassRunner.AfterTestClassStartingAsync"/>.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestClassRunner.InitializedAsyncFixtures">
            <summary>
            Gets the already initialized async fixtures <see cref="M:Xunit.Sdk.XunitTestClassRunner.CreateClassFixtureAsync(System.Type)"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestClassRunner.CreateClassFixture(System.Type)">
            <summary>
            Creates the instance of a class fixture type to be used by the test class. If the fixture can be created,
            it should be placed into the <see cref="P:Xunit.Sdk.XunitTestClassRunner.ClassFixtureMappings"/> dictionary; if it cannot, then the method
            should record the error by calling <code>Aggregator.Add</code>.
            </summary>
            <param name="fixtureType">The type of the fixture to be created</param>
        </member>
        <member name="M:Xunit.Sdk.XunitTestClassRunner.FormatConstructorArgsMissingMessage(System.Reflection.ConstructorInfo,System.Collections.Generic.IReadOnlyList{System.Tuple{System.Int32,System.Reflection.ParameterInfo}})">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestClassRunner.AfterTestClassStartingAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestClassRunner.BeforeTestClassFinishedAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestClassRunner.RunTestMethodAsync(Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IReflectionMethodInfo,System.Collections.Generic.IEnumerable{Xunit.Sdk.IXunitTestCase},System.Object[])">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestClassRunner.SelectTestClassConstructor">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestClassRunner.TryGetConstructorArgument(System.Reflection.ConstructorInfo,System.Int32,System.Reflection.ParameterInfo,System.Object@)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestCollectionRunner">
            <summary>
            The test collection runner for xUnit.net v2 tests.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCollectionRunner.#ctor(Xunit.Abstractions.ITestCollection,System.Collections.Generic.IEnumerable{Xunit.Sdk.IXunitTestCase},Xunit.Abstractions.IMessageSink,Xunit.Sdk.IMessageBus,Xunit.Sdk.ITestCaseOrderer,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestCollectionRunner"/> class.
            </summary>
            <param name="testCollection">The test collection that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="testCaseOrderer">The test case orderer that will be used to decide how to order the test.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCollectionRunner.CollectionFixtureMappings">
            <summary>
            Gets the fixture mappings that were created during <see cref="M:Xunit.Sdk.XunitTestCollectionRunner.AfterTestCollectionStartingAsync"/>.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCollectionRunner.DiagnosticMessageSink">
            <summary>
            Gets the message sink used to send diagnostic messages.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCollectionRunner.AfterTestCollectionStartingAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCollectionRunner.BeforeTestCollectionFinishedAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCollectionRunner.CreateCollectionFixture(System.Type)">
            <summary>
            Creates the instance of a collection fixture type to be used by the test collection. If the fixture can be created,
            it should be placed into the <see cref="P:Xunit.Sdk.XunitTestCollectionRunner.CollectionFixtureMappings"/> dictionary; if it cannot, then the method
            should record the error by calling <code>Aggregator.Add</code>.
            </summary>
            <param name="fixtureType">The type of the fixture to be created</param>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCollectionRunner.GetTestCaseOrderer">
            <summary>
            Gives an opportunity to override test case orderer. By default, this method gets the
            orderer from the collection definition. If this function returns <c>null</c>, the
            test case orderer passed into the constructor will be used.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCollectionRunner.RunTestClassAsync(Xunit.Abstractions.ITestClass,Xunit.Abstractions.IReflectionTypeInfo,System.Collections.Generic.IEnumerable{Xunit.Sdk.IXunitTestCase})">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestInvoker">
            <summary>
            The test invoker for xUnit.net v2 tests.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestInvoker.#ctor(Xunit.Abstractions.ITest,Xunit.Sdk.IMessageBus,System.Type,System.Object[],System.Reflection.MethodInfo,System.Object[],System.Collections.Generic.IReadOnlyList{Xunit.Sdk.BeforeAfterTestAttribute},Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestInvoker"/> class.
            </summary>
            <param name="test">The test that this invocation belongs to.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="testClass">The test class that the test method belongs to.</param>
            <param name="constructorArguments">The arguments to be passed to the test class constructor.</param>
            <param name="testMethod">The test method that will be invoked.</param>
            <param name="testMethodArguments">The arguments to be passed to the test method.</param>
            <param name="beforeAfterAttributes">The list of <see cref="T:Xunit.Sdk.BeforeAfterTestAttribute"/>s for this test invocation.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTestInvoker.BeforeAfterAttributes">
            <summary>
            Gets the list of <see cref="T:Xunit.Sdk.BeforeAfterTestAttribute"/>s for this test invocation.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestInvoker.BeforeTestMethodInvokedAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestInvoker.AfterTestMethodInvokedAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestInvoker.InvokeTestMethodAsync(System.Object)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestMethodRunner">
            <summary>
            The test method runner for xUnit.net v2 tests.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestMethodRunner.#ctor(Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IReflectionTypeInfo,Xunit.Abstractions.IReflectionMethodInfo,System.Collections.Generic.IEnumerable{Xunit.Sdk.IXunitTestCase},Xunit.Abstractions.IMessageSink,Xunit.Sdk.IMessageBus,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestMethodRunner"/> class.
            </summary>
            <param name="testMethod">The test method to be run.</param>
            <param name="class">The test class that contains the test method.</param>
            <param name="method">The test method that contains the tests to be run.</param>
            <param name="testCases">The test cases to be run.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages to.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
            <param name="constructorArguments">The constructor arguments for the test class.</param>
        </member>
        <member name="M:Xunit.Sdk.XunitTestMethodRunner.RunTestCaseAsync(Xunit.Sdk.IXunitTestCase)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestRunner">
            <summary>
            The test runner for xUnit.net v2 tests.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestRunner.#ctor(Xunit.Abstractions.ITest,Xunit.Sdk.IMessageBus,System.Type,System.Object[],System.Reflection.MethodInfo,System.Object[],System.String,System.Collections.Generic.IReadOnlyList{Xunit.Sdk.BeforeAfterTestAttribute},Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestRunner"/> class.
            </summary>
            <param name="test">The test that this invocation belongs to.</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="testClass">The test class that the test method belongs to.</param>
            <param name="constructorArguments">The arguments to be passed to the test class constructor.</param>
            <param name="testMethod">The test method that will be invoked.</param>
            <param name="testMethodArguments">The arguments to be passed to the test method.</param>
            <param name="skipReason">The skip reason, if the test is to be skipped.</param>
            <param name="beforeAfterAttributes">The list of <see cref="T:Xunit.Sdk.BeforeAfterTestAttribute"/>s for this test.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTestRunner.BeforeAfterAttributes">
            <summary>
            Gets the list of <see cref="T:Xunit.Sdk.BeforeAfterTestAttribute"/>s for this test.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestRunner.InvokeTestAsync(Xunit.Sdk.ExceptionAggregator)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestRunner.InvokeTestMethodAsync(Xunit.Sdk.ExceptionAggregator)">
            <summary>
            Override this method to invoke the test method.
            </summary>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <returns>Returns the execution time (in seconds) spent running the test method.</returns>
        </member>
        <member name="T:Xunit.Sdk.XunitTheoryTestCaseRunner">
            <summary>
            The test case runner for xUnit.net v2 theories (which could not be pre-enumerated;
            pre-enumerated test cases use <see cref="T:Xunit.Sdk.XunitTestCaseRunner"/>).
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTheoryTestCaseRunner.#ctor(Xunit.Sdk.IXunitTestCase,System.String,System.String,System.Object[],Xunit.Abstractions.IMessageSink,Xunit.Sdk.IMessageBus,Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTheoryTestCaseRunner"/> class.
            </summary>
            <param name="testCase">The test case to be run.</param>
            <param name="displayName">The display name of the test case.</param>
            <param name="skipReason">The skip reason, if the test is to be skipped.</param>
            <param name="constructorArguments">The arguments to be passed to the test class constructor.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="messageBus">The message bus to report run status to.</param>
            <param name="aggregator">The exception aggregator used to run code and collect exceptions.</param>
            <param name="cancellationTokenSource">The task cancellation token source, used to cancel the test run.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTheoryTestCaseRunner.DiagnosticMessageSink">
            <summary>
            Gets the message sink used to report <see cref="T:Xunit.Abstractions.IDiagnosticMessage"/> messages.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTheoryTestCaseRunner.AfterTestCaseStartingAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTheoryTestCaseRunner.BeforeTestCaseFinishedAsync">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTheoryTestCaseRunner.RunTestAsync">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestAssembly">
            <summary>
            The default implementation of <see cref="T:Xunit.Abstractions.ITestAssembly"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssembly.#ctor">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TestAssembly.#ctor(Xunit.Abstractions.IAssemblyInfo,System.String,System.Version)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestAssembly"/> class.
            </summary>
            <param name="assembly">The test assembly.</param>
            <param name="configFileName">The optional configuration filename (defaults to the
            configuration file of the current app domain if not provided)</param>
            <param name="version">The version number of the assembly (defaults to "0.0.0.0")</param>
        </member>
        <member name="P:Xunit.Sdk.TestAssembly.Assembly">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssembly.ConfigFileName">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssembly.Version">
            <summary>
            Gets or sets the assembly version.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssembly.Serialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestAssembly.Deserialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestClass">
            <summary>
            The default implementation of <see cref="T:Xunit.Abstractions.ITestClass"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClass.#ctor">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TestClass.#ctor(Xunit.Abstractions.ITestCollection,Xunit.Abstractions.ITypeInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClass"/> class.
            </summary>
            <param name="testCollection">The test collection the class belongs to</param>
            <param name="class">The test class</param>
        </member>
        <member name="P:Xunit.Sdk.TestClass.Class">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestClass.TestCollection">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestClass.Serialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestClass.Deserialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestClassComparer">
            <summary>
            An implementation of <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> for <see cref="T:Xunit.Abstractions.ITestClass"/>.
            Compares the fully qualified names of the types.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.TestClassComparer.Instance">
            <summary>
            The singleton instance of the comparer.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassComparer.Equals(Xunit.Abstractions.ITestClass,Xunit.Abstractions.ITestClass)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestClassComparer.GetHashCode(Xunit.Abstractions.ITestClass)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCollection">
            <summary>
            The default implementation of <see cref="T:Xunit.Abstractions.ITestCollection"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollection.#ctor">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TestCollection.#ctor(Xunit.Abstractions.ITestAssembly,Xunit.Abstractions.ITypeInfo,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCollection"/> class.
            </summary>
            <param name="testAssembly">The test assembly the collection belongs to</param>
            <param name="collectionDefinition">The optional type which contains the collection definition</param>
            <param name="displayName">The display name for the test collection</param>
        </member>
        <member name="M:Xunit.Sdk.TestCollection.#ctor(Xunit.Abstractions.ITestAssembly,Xunit.Abstractions.ITypeInfo,System.String,System.Nullable{System.Guid})">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCollection"/> class.
            </summary>
            <param name="testAssembly">The test assembly the collection belongs to</param>
            <param name="collectionDefinition">The optional type which contains the collection definition</param>
            <param name="displayName">The display name for the test collection</param>
            <param name="uniqueId">The test collection's unique ID</param>
        </member>
        <member name="P:Xunit.Sdk.TestCollection.CollectionDefinition">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCollection.DisplayName">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCollection.TestAssembly">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCollection.UniqueID">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestCollection.Serialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestCollection.Deserialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCollectionComparer">
            <summary>
            An implementation of <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> for <see cref="T:Xunit.Abstractions.ITestCollection"/>.
            Compares the IDs of the test collections.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.TestCollectionComparer.Instance">
            <summary>
            The singleton instance of the comparer.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionComparer.Equals(Xunit.Abstractions.ITestCollection,Xunit.Abstractions.ITestCollection)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionComparer.GetHashCode(Xunit.Abstractions.ITestCollection)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCollectionFactoryHelper">
            <summary>
            A helper class that gets the list of test collection definitions for a given assembly.
            Reports any misconfigurations of the test assembly via the diagnostic message sink.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionFactoryHelper.GetTestCollectionDefinitions(Xunit.Abstractions.IAssemblyInfo,Xunit.Abstractions.IMessageSink)">
            <summary>
            Gets the test collection definitions for the given assembly.
            </summary>
            <param name="assemblyInfo">The assembly.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <returns>A list of mappings from test collection name to test collection definitions (as <see cref="T:Xunit.Abstractions.ITypeInfo"/></returns>
        </member>
        <member name="T:Xunit.Sdk.TestFramework">
            <summary>
            A default implementation of <see cref="T:Xunit.Abstractions.ITestFramework"/> that tracks objects to be
            disposed when the framework is disposed. The discoverer and executor are automatically
            tracked for disposal, since those interfaces mandate an implementation of <see cref="T:System.IDisposable"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestFramework.#ctor(Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestFramework"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
        </member>
        <member name="P:Xunit.Sdk.TestFramework.DiagnosticMessageSink">
            <summary>
            Gets the message sink used to send diagnostic messages.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFramework.DisposalTracker">
            <summary>
            Gets the disposal tracker for the test framework.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFramework.SourceInformationProvider">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFramework.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFramework.CreateDiscoverer(Xunit.Abstractions.IAssemblyInfo)">
            <summary>
            Override this method to provide the implementation of <see cref="T:Xunit.Abstractions.ITestFrameworkDiscoverer"/>.
            </summary>
            <param name="assemblyInfo">The assembly that is being discovered.</param>
            <returns>Returns the test framework discoverer.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestFramework.CreateExecutor(System.Reflection.AssemblyName)">
            <summary>
            Override this method to provide the implementation of <see cref="T:Xunit.Abstractions.ITestFrameworkExecutor"/>.
            </summary>
            <param name="assemblyName">The assembly that is being executed.</param>
            <returns>Returns the test framework executor.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestFramework.GetDiscoverer(Xunit.Abstractions.IAssemblyInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFramework.GetExecutor(System.Reflection.AssemblyName)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestFrameworkDiscoverer">
            <summary>
            A base implementation of <see cref="T:Xunit.Abstractions.ITestFrameworkDiscoverer"/> that supports test filtering
            and runs the discovery process on a thread pool thread.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscoverer.#ctor(Xunit.Abstractions.IAssemblyInfo,Xunit.Abstractions.ISourceInformationProvider,Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestFrameworkDiscoverer"/> class.
            </summary>
            <param name="assemblyInfo">The test assembly.</param>
            <param name="sourceProvider">The source information provider.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkDiscoverer.AssemblyInfo">
            <summary>
            Gets the assembly that's being discovered.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkDiscoverer.DiagnosticMessageSink">
            <summary>
            Gets the message sink used to report diagnostic messages.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkDiscoverer.DisposalTracker">
            <summary>
            Gets the disposal tracker for the test framework discoverer.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkDiscoverer.SourceProvider">
            <summary>
            Get the source code information provider used during discovery.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkDiscoverer.TargetFramework">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkDiscoverer.TestFrameworkDisplayName">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscoverer.CreateTestClass(Xunit.Abstractions.ITypeInfo)">
            <summary>
            Implement this method to create a test class for the given CLR type.
            </summary>
            <param name="class">The CLR type.</param>
            <returns>The test class.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscoverer.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscoverer.Find(System.Boolean,Xunit.Abstractions.IMessageSink,Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscoverer.Find(System.String,System.Boolean,Xunit.Abstractions.IMessageSink,Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscoverer.FindTestsForType(Xunit.Abstractions.ITestClass,System.Boolean,Xunit.Sdk.IMessageBus,Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Core implementation to discover unit tests in a given test class.
            </summary>
            <param name="testClass">The test class.</param>
            <param name="includeSourceInformation">Set to <c>true</c> to attempt to include source information.</param>
            <param name="messageBus">The message sink to send discovery messages to.</param>
            <param name="discoveryOptions">The options used by the test framework during discovery.</param>
            <returns>Returns <c>true</c> if discovery should continue; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscoverer.IsValidTestClass(Xunit.Abstractions.ITypeInfo)">
            <summary>
            Determines if a type should be used for discovery. Can be used to filter out types that
            are not desirable. The default implementation filters out abstract (non-static) classes.
            </summary>
            <param name="type">The type.</param>
            <returns>Returns <c>true</c> if the type can contain tests; <c>false</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscoverer.ReportDiscoveredTestCase(Xunit.Abstractions.ITestCase,System.Boolean,Xunit.Sdk.IMessageBus)">
            <summary>
            Reports a discovered test case to the message bus, after updating the source code information
            (if desired).
            </summary>
            <param name="testCase"></param>
            <param name="includeSourceInformation"></param>
            <param name="messageBus"></param>
            <returns></returns>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscoverer.Serialize(Xunit.Abstractions.ITestCase)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestFrameworkExecutor`1">
            <summary>
            A reusable implementation of <see cref="T:Xunit.Abstractions.ITestFrameworkExecutor"/> which contains the basic behavior
            for running tests.
            </summary>
            <typeparam name="TTestCase">The type of the test case used by the test framework. Must
            derive from <see cref="T:Xunit.Abstractions.ITestCase"/>.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkExecutor`1.#ctor(System.Reflection.AssemblyName,Xunit.Abstractions.ISourceInformationProvider,Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestFrameworkExecutor`1"/> class.
            </summary>
            <param name="assemblyName">Name of the test assembly.</param>
            <param name="sourceInformationProvider">The source line number information provider.</param>
            <param name="diagnosticMessageSink">The message sink to report diagnostic messages to.</param>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkExecutor`1.AssemblyInfo">
            <summary>
            Gets the assembly information of the assembly under test.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkExecutor`1.DiagnosticMessageSink">
            <summary>
            Gets the message sink to send diagnostic messages to.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkExecutor`1.DisposalTracker">
            <summary>
            Gets the disposal tracker for the test framework discoverer.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkExecutor`1.SourceInformationProvider">
            <summary>
            Gets the source information provider.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkExecutor`1.CreateDiscoverer">
            <summary>
            Override to create a test framework discoverer that can be used to discover
            tests when the user asks to run all test.
            </summary>
            <returns>The test framework discoverer</returns>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkExecutor`1.Deserialize(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkExecutor`1.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkExecutor`1.RunAll(Xunit.Abstractions.IMessageSink,Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkExecutor`1.RunTests(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.IMessageSink,Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkExecutor`1.RunTestCases(System.Collections.Generic.IEnumerable{`0},Xunit.Abstractions.IMessageSink,Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <summary>
            Override to run test cases.
            </summary>
            <param name="testCases">The test cases to be run.</param>
            <param name="executionMessageSink">The message sink to report run status to.</param>
            <param name="executionOptions">The user's requested execution options.</param>
        </member>
        <member name="T:Xunit.Sdk.TestFrameworkProxy">
            <summary>
            This class proxies for the real implementation of <see cref="T:Xunit.Abstractions.ITestFramework"/>, based on
            whether the user has overridden the choice via <see cref="T:Xunit.TestFrameworkAttribute"/>. If
            no attribute is found, defaults to <see cref="T:Xunit.Sdk.XunitTestFramework"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkProxy.#ctor(System.Object,System.Object,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestFrameworkProxy"/> class.
            </summary>
            <param name="testAssemblyObject">The test assembly (expected to implement <see cref="T:Xunit.Abstractions.IAssemblyInfo"/>).</param>
            <param name="sourceInformationProviderObject">The source information provider (expected to implement <see cref="T:Xunit.Abstractions.ISourceInformationProvider"/>).</param>
            <param name="diagnosticMessageSinkObject">The diagnostic message sink (expected to implement <see cref="T:Xunit.Abstractions.IMessageSink"/>).</param>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkProxy.InnerTestFramework">
            <summary>
            Gets the test framework that's being wrapped by the proxy.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFrameworkProxy.SourceInformationProvider">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkProxy.GetDiscoverer(Xunit.Abstractions.IAssemblyInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkProxy.GetExecutor(System.Reflection.AssemblyName)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkProxy.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestFrameworkProxy.MessageSinkWrapper">
            <summary>
            INTERNAL CLASS. DO NOT USE.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.TestFrameworkProxy.MessageSinkWrapper.InnerSink">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkProxy.MessageSinkWrapper.#ctor(Xunit.Abstractions.IMessageSink)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkProxy.MessageSinkWrapper.OnMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.TestMethod">
            <summary>
            The default implementation of <see cref="T:Xunit.Abstractions.ITestMethod"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethod.#ctor">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TestMethod.#ctor(Xunit.Abstractions.ITestClass,Xunit.Abstractions.IMethodInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestMethod"/> class.
            </summary>
            <param name="class">The test class</param>
            <param name="method">The test method</param>
        </member>
        <member name="P:Xunit.Sdk.TestMethod.Method">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethod.TestClass">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestMethod.Serialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestMethod.Deserialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestMethodComparer">
            <summary>
            An implementation of <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> for <see cref="T:Xunit.Abstractions.ITestMethod"/>.
            Compares the names of the methods.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.TestMethodComparer.Instance">
            <summary>
            The singleton instance of the comparer.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodComparer.Equals(Xunit.Abstractions.ITestMethod,Xunit.Abstractions.ITestMethod)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestMethodComparer.GetHashCode(Xunit.Abstractions.ITestMethod)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestMethodTestCase">
            <summary>
            A base class implementation of <see cref="T:Xunit.Abstractions.ITestCase"/> which is based on test cases being
            related directly to test methods.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.#ctor">
            <summary>
            Used for de-serialization.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.#ctor(Xunit.Sdk.TestMethodDisplay,Xunit.Abstractions.ITestMethod,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestMethodTestCase"/> class.
            </summary>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="testMethod">The test method this test case belongs to.</param>
            <param name="testMethodArguments">The arguments for the test method.</param>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.#ctor(Xunit.Sdk.TestMethodDisplay,Xunit.Sdk.TestMethodDisplayOptions,Xunit.Abstractions.ITestMethod,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestMethodTestCase"/> class.
            </summary>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="defaultMethodDisplayOptions">Default method display options to use (when not customized).</param>
            <param name="testMethod">The test method this test case belongs to.</param>
            <param name="testMethodArguments">The arguments for the test method.</param>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.BaseDisplayName">
            <summary>
            Returns the base display name for a test ("TestClassName.MethodName").
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.DefaultMethodDisplay">
            <summary>
            Returns the default method display to use (when not customized).
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.DefaultMethodDisplayOptions">
            <summary>
            Returns the default method display options to use (when not customized).
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.DisplayName">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.InitializationException">
            <summary>
            Gets or sets the exception that happened during initialization. When this is set, then
            the test execution should fail with this exception.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.Method">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.MethodGenericTypes">
            <summary>
            Gets the generic types that were used to close the generic test method, if
            applicable; <c>null</c>, if the test method was not an open generic.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.SkipReason">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.SourceInformation">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.TestMethod">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.TestMethodArguments">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.Traits">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodTestCase.UniqueID">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.EnsureInitialized">
            <summary>
            Call to ensure the object is fully initialized().
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.GetUniqueID">
            <summary>
            Gets the unique ID for the test case.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.BytesToHexString(System.Byte[])">
            <summary>Converts an array of bytes to its hexadecimal value as a string.</summary>
            <param name="bytes">The bytes.</param>
            <returns>A string containing the hexadecimal representation of the provided bytes.</returns>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.NibbleToHexChar(System.Int32)">
            <summary>Gets a hexadecimal digit character from the 4-bit value.</summary>
            <param name="b">A value in the range [0, 15].</param>
            <returns>A character in the range ['0','9'] or ['a','f'].</returns>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.Initialize">
            <summary>
            Called when initializing the test cases, either after constructor or de-serialization.
            Override this method to add additional initialization-time work.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.Serialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestMethodTestCase.Deserialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestOutputHelper">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestOutputHelper"/>.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestOutputHelper.Output">
            <summary>
            Gets the output provided by the test.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestOutputHelper.Initialize(Xunit.Sdk.IMessageBus,Xunit.Abstractions.ITest)">
            <summary>
            Initialize the test output helper with information about a test.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestOutputHelper.Uninitialize">
            <summary>
            Resets the test output helper to its uninitialized state.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestOutputHelper.WriteLine(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestOutputHelper.WriteLine(System.String,System.Object[])">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestTimeoutException">
            <summary>
            Thrown if a test exceeds the specified timeout.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestTimeoutException.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Xunit.Sdk.TestTimeoutException"/>.
            </summary>
            <param name="timeout">The timeout that was exceeded, in milliseconds</param>
        </member>
        <member name="T:Xunit.Sdk.TheoryDiscoverer">
            <summary>
            Implementation of <see cref="T:Xunit.Sdk.IXunitTestCaseDiscoverer"/> that supports finding test cases
            on methods decorated with <see cref="T:Xunit.TheoryAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.#ctor(Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TheoryDiscoverer"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
        </member>
        <member name="P:Xunit.Sdk.TheoryDiscoverer.DiagnosticMessageSink">
            <summary>
            Gets the message sink to be used to send diagnostic messages.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCaseForDataRow(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo,System.Object[])">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCasesForDataRow(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo,System.Object[])">
            <summary>
            Creates test cases for a single row of data. By default, returns a single instance of <see cref="T:Xunit.Sdk.XunitTestCase"/>
            with the data row inside of it.
            </summary>
            <param name="discoveryOptions">The discovery options to be used.</param>
            <param name="testMethod">The test method the test cases belong to.</param>
            <param name="theoryAttribute">The theory attribute attached to the test method.</param>
            <param name="dataRow">The row of data for this test case.</param>
            <returns>The test cases</returns>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCaseForSkip(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo,System.String)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCasesForSkip(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo,System.String)">
            <summary>
            Creates test cases for a skipped theory. By default, returns a single instance of <see cref="T:Xunit.Sdk.XunitTestCase"/>
            (which inherently discovers the skip reason via the fact attribute).
            </summary>
            <param name="discoveryOptions">The discovery options to be used.</param>
            <param name="testMethod">The test method the test cases belong to.</param>
            <param name="theoryAttribute">The theory attribute attached to the test method.</param>
            <param name="skipReason">The skip reason that decorates <paramref name="theoryAttribute"/>.</param>
            <returns>The test cases</returns>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCaseForTheory(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCasesForTheory(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Creates test cases for the entire theory. This is used when one or more of the theory data items
            are not serializable, or if the user has requested to skip theory pre-enumeration. By default,
            returns a single instance of <see cref="T:Xunit.Sdk.XunitTheoryTestCase"/>, which performs the data discovery
            at runtime.
            </summary>
            <param name="discoveryOptions">The discovery options to be used.</param>
            <param name="testMethod">The test method the test cases belong to.</param>
            <param name="theoryAttribute">The theory attribute attached to the test method.</param>
            <returns>The test case</returns>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCaseForSkippedDataRow(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo,System.Object[],System.String)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCasesForSkippedDataRow(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo,System.Object[],System.String)">
            <summary>
            Creates test cases for a single row of skipped data. By default, returns a single instance of <see cref="T:Xunit.Sdk.XunitSkippedDataRowTestCase"/>
            with the data row inside of it.
            </summary>
            <remarks>If this method is overridden, the implementation will have to override <see cref="P:Xunit.Sdk.TestMethodTestCase.SkipReason"/> otherwise
            the default behavior will look at the <see cref="T:Xunit.TheoryAttribute"/> and the test case will not be skipped.</remarks>
            <param name="discoveryOptions">The discovery options to be used.</param>
            <param name="testMethod">The test method the test cases belong to.</param>
            <param name="theoryAttribute">The theory attribute attached to the test method.</param>
            <param name="dataRow">The row of data for this test case.</param>
            <param name="skipReason">The reason this test case is to be skipped</param>
            <returns>The test cases</returns>
        </member>
        <member name="M:Xunit.Sdk.TheoryDiscoverer.Discover(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Discover test cases from a test method.
            </summary>
            <remarks>
            This method performs the following steps:
            - If the theory attribute is marked with Skip, returns the single test case from <see cref="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCaseForSkip(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo,System.String)"/>;
            - If pre-enumeration is off, or any of the test data is non serializable, returns the single test case from <see cref="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCaseForTheory(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo)"/>;
            - If there is no theory data, returns a single test case of <see cref="T:Xunit.Sdk.ExecutionErrorTestCase"/> with the error in it;
            - Otherwise, it returns one test case per data row, created by calling <see cref="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCaseForDataRow(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo,System.Object[])"/> or <see cref="M:Xunit.Sdk.TheoryDiscoverer.CreateTestCaseForSkippedDataRow(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo,System.Object[],System.String)"/> if the data attribute has a skip reason.
            </remarks>
            <param name="discoveryOptions">The discovery options to be used.</param>
            <param name="testMethod">The test method the test cases belong to.</param>
            <param name="theoryAttribute">The theory attribute attached to the test method.</param>
            <returns>Returns zero or more test cases represented by the test method.</returns>
        </member>
        <member name="T:Xunit.Sdk.XunitSkippedDataRowTestCase">
            <summary>
            Represents a test case that had a valid data row, but the data row was generated by a data attribute with the skip property set.
            </summary>
            <remarks>This class is only ever used if the discoverer is pre-enumerating theories and the data row is serializable.</remarks>
        </member>
        <member name="M:Xunit.Sdk.XunitSkippedDataRowTestCase.#ctor">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.XunitSkippedDataRowTestCase.#ctor(Xunit.Abstractions.IMessageSink,Xunit.Sdk.TestMethodDisplay,Xunit.Abstractions.ITestMethod,System.String,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitSkippedDataRowTestCase"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="testMethod">The test method this test case belongs to.</param>
            <param name="skipReason">The reason that this test case will be skipped</param>
            <param name="testMethodArguments">The arguments for the test method.</param>
        </member>
        <member name="M:Xunit.Sdk.XunitSkippedDataRowTestCase.#ctor(Xunit.Abstractions.IMessageSink,Xunit.Sdk.TestMethodDisplay,Xunit.Sdk.TestMethodDisplayOptions,Xunit.Abstractions.ITestMethod,System.String,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitSkippedDataRowTestCase"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="defaultMethodDisplayOptions">Default method display options to use (when not customized).</param>
            <param name="testMethod">The test method this test case belongs to.</param>
            <param name="skipReason">The reason that this test case will be skipped</param>
            <param name="testMethodArguments">The arguments for the test method.</param>
        </member>
        <member name="M:Xunit.Sdk.XunitSkippedDataRowTestCase.Deserialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitSkippedDataRowTestCase.GetSkipReason(Xunit.Abstractions.IAttributeInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitSkippedDataRowTestCase.Serialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTest">
            <summary>
            An implementation of <see cref="T:Xunit.Abstractions.ITest"/> for xUnit v2.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTest.#ctor(Xunit.Sdk.IXunitTestCase,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTest"/> class.
            </summary>
            <param name="testCase">The test case this test belongs to.</param>
            <param name="displayName">The display name for this test.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTest.DisplayName">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.XunitTest.TestCase">
            <summary>
            Gets the xUnit v2 test case.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTest.Xunit#Abstractions#ITest#TestCase">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestCase">
            <summary>
            Default implementation of <see cref="T:Xunit.Sdk.IXunitTestCase"/> for xUnit v2 that supports tests decorated with
            both <see cref="T:Xunit.FactAttribute"/> and <see cref="T:Xunit.TheoryAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.#ctor">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.#ctor(Xunit.Abstractions.IMessageSink,Xunit.Sdk.TestMethodDisplay,Xunit.Abstractions.ITestMethod,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestCase"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="testMethod">The test method this test case belongs to.</param>
            <param name="testMethodArguments">The arguments for the test method.</param>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.#ctor(Xunit.Abstractions.IMessageSink,Xunit.Sdk.TestMethodDisplay,Xunit.Sdk.TestMethodDisplayOptions,Xunit.Abstractions.ITestMethod,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestCase"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="defaultMethodDisplayOptions">Default method display options to use (when not customized).</param>
            <param name="testMethod">The test method this test case belongs to.</param>
            <param name="testMethodArguments">The arguments for the test method.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCase.DiagnosticMessageSink">
            <summary>
            Gets the message sink used to report <see cref="T:Xunit.Abstractions.IDiagnosticMessage"/> messages.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestCase.Timeout">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.GetDisplayName(Xunit.Abstractions.IAttributeInfo,System.String)">
            <summary>
            Gets the display name for the test case. Calls <see cref="M:Xunit.Sdk.TypeUtility.GetDisplayNameWithArguments(Xunit.Abstractions.IMethodInfo,System.String,System.Object[],Xunit.Abstractions.ITypeInfo[])"/>
            with the given base display name (which is itself either derived from <see cref="P:Xunit.FactAttribute.DisplayName"/>,
            falling back to <see cref="P:Xunit.Sdk.TestMethodTestCase.BaseDisplayName"/>.
            </summary>
            <param name="factAttribute">The fact attribute the decorated the test case.</param>
            <param name="displayName">The base display name from <see cref="P:Xunit.Sdk.TestMethodTestCase.BaseDisplayName"/>.</param>
            <returns>The display name for the test case.</returns>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.GetSkipReason(Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Gets the skip reason for the test case. By default, pulls the skip reason from the
            <see cref="P:Xunit.FactAttribute.Skip"/> property.
            </summary>
            <param name="factAttribute">The fact attribute the decorated the test case.</param>
            <returns>The skip reason, if skipped; <c>null</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.GetTimeout(Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Gets the timeout for the test case. By default, pulls the skip reason from the
            <see cref="P:Xunit.FactAttribute.Timeout"/> property.
            </summary>
            <param name="factAttribute">The fact attribute the decorated the test case.</param>
            <returns>The timeout in milliseconds, if set; 0, if unset.</returns>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.Initialize">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.RunAsync(Xunit.Abstractions.IMessageSink,Xunit.Sdk.IMessageBus,System.Object[],Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.Serialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCase.Deserialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestFramework">
            <summary>
            The implementation of <see cref="T:Xunit.Abstractions.ITestFramework"/> that supports discovery and
            execution of unit tests linked against xunit.core.dll, using xunit.execution.dll.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFramework.#ctor(Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestFramework"/> class.
            </summary>
            <param name="messageSink">The message sink used to send diagnostic messages</param>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFramework.CreateDiscoverer(Xunit.Abstractions.IAssemblyInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFramework.CreateExecutor(System.Reflection.AssemblyName)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestFrameworkDiscoverer">
            <summary>
            The implementation of <see cref="T:Xunit.Abstractions.ITestFrameworkDiscoverer"/> that supports discovery
            of unit tests linked against xunit.core.dll, using xunit.execution.dll.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.XunitTestFrameworkDiscoverer.DisplayName">
            <summary>
            Gets the display name of the xUnit.net v2 test framework.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkDiscoverer.#ctor(Xunit.Abstractions.IAssemblyInfo,Xunit.Abstractions.ISourceInformationProvider,Xunit.Abstractions.IMessageSink,Xunit.Sdk.IXunitTestCollectionFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestFrameworkDiscoverer"/> class.
            </summary>
            <param name="assemblyInfo">The test assembly.</param>
            <param name="sourceProvider">The source information provider.</param>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="collectionFactory">The test collection factory used to look up test collections.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTestFrameworkDiscoverer.DiscovererTypeCache">
            <summary>
            Gets the mapping dictionary of fact attribute type to discoverer type.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.XunitTestFrameworkDiscoverer.TestCollectionFactory">
            <summary>
            Gets the test collection factory that makes test collections.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkDiscoverer.CreateTestClass(Xunit.Abstractions.ITypeInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkDiscoverer.FindTestsForMethod(Xunit.Abstractions.ITestMethod,System.Boolean,Xunit.Sdk.IMessageBus,Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <summary>
            Finds the tests on a test method.
            </summary>
            <param name="testMethod">The test method.</param>
            <param name="includeSourceInformation">Set to <c>true</c> to indicate that source information should be included.</param>
            <param name="messageBus">The message bus to report discovery messages to.</param>
            <param name="discoveryOptions">The options used by the test framework during discovery.</param>
            <returns>Return <c>true</c> to continue test discovery, <c>false</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkDiscoverer.FindTestsForType(Xunit.Abstractions.ITestClass,System.Boolean,Xunit.Sdk.IMessageBus,Xunit.Abstractions.ITestFrameworkDiscoveryOptions)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkDiscoverer.GetDiscoverer(System.Type)">
            <summary>
            Gets the test case discover instance for the given discoverer type. The instances are cached
            and reused, since they should not be stateful.
            </summary>
            <param name="discovererType">The discoverer type.</param>
            <returns>Returns the test case discoverer instance.</returns>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkDiscoverer.Serialize(Xunit.Abstractions.ITestCase)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTestFrameworkExecutor">
            <summary>
            The implementation of <see cref="T:Xunit.Abstractions.ITestFrameworkExecutor"/> that supports execution
            of unit tests linked against xunit.core.dll, using xunit.execution.dll.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkExecutor.#ctor(System.Reflection.AssemblyName,Xunit.Abstractions.ISourceInformationProvider,Xunit.Abstractions.IMessageSink)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTestFrameworkExecutor"/> class.
            </summary>
            <param name="assemblyName">Name of the test assembly.</param>
            <param name="sourceInformationProvider">The source line number information provider.</param>
            <param name="diagnosticMessageSink">The message sink to report diagnostic messages to.</param>
        </member>
        <member name="P:Xunit.Sdk.XunitTestFrameworkExecutor.TestAssembly">
            <summary>
            Gets the test assembly that contains the test.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkExecutor.CreateDiscoverer">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkExecutor.Deserialize(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.XunitTestFrameworkExecutor.RunTestCases(System.Collections.Generic.IEnumerable{Xunit.Sdk.IXunitTestCase},Xunit.Abstractions.IMessageSink,Xunit.Abstractions.ITestFrameworkExecutionOptions)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.XunitTheoryTestCase">
            <summary>
            Represents a test case which runs multiple tests for theory data, either because the
            data was not enumerable or because the data was not serializable.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTheoryTestCase.#ctor">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.XunitTheoryTestCase.#ctor(Xunit.Abstractions.IMessageSink,Xunit.Sdk.TestMethodDisplay,Xunit.Abstractions.ITestMethod)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTheoryTestCase"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="testMethod">The method under test.</param>
        </member>
        <member name="M:Xunit.Sdk.XunitTheoryTestCase.#ctor(Xunit.Abstractions.IMessageSink,Xunit.Sdk.TestMethodDisplay,Xunit.Sdk.TestMethodDisplayOptions,Xunit.Abstractions.ITestMethod)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.XunitTheoryTestCase"/> class.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages</param>
            <param name="defaultMethodDisplay">Default method display to use (when not customized).</param>
            <param name="defaultMethodDisplayOptions">Default method display options to use (when not customized).</param>
            <param name="testMethod">The method under test.</param>
        </member>
        <member name="M:Xunit.Sdk.XunitTheoryTestCase.RunAsync(Xunit.Abstractions.IMessageSink,Xunit.Sdk.IMessageBus,System.Object[],Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <inheritdoc />
        </member>
        <member name="T:Xunit.Sdk.MaxConcurrencySyncContext">
            <summary>
            An implementation of <see cref="T:System.Threading.SynchronizationContext"/> which runs work on custom threads
            rather than in the thread pool, and limits the number of in-flight actions.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.MaxConcurrencySyncContext.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.MaxConcurrencySyncContext"/> class.
            </summary>
            <param name="maximumConcurrencyLevel">The maximum number of tasks to run at any one time.</param>
        </member>
        <member name="P:Xunit.Sdk.MaxConcurrencySyncContext.IsSupported">
            <summary>
            Gets a flag indicating whether maximum concurrency is supported.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.MaxConcurrencySyncContext.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.MaxConcurrencySyncContext.Post(System.Threading.SendOrPostCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.MaxConcurrencySyncContext.Send(System.Threading.SendOrPostCallback,System.Object)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.MessageBus">
            <summary>
            This is an internal class, and is not intended to be called from end-user code.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.MessageBus.#ctor(Xunit.Abstractions.IMessageSink,System.Boolean)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.MessageBus.Dispose">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.MessageBus.QueueMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.DelegatingMessageBus">
            <summary>
            Implementation of <see cref="T:Xunit.Sdk.IMessageBus" /> that delegates to another implementation of
            <see cref="T:Xunit.Sdk.IMessageBus" /> while calling into a callback for each message.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageBus.#ctor(Xunit.Sdk.IMessageBus,System.Action{Xunit.Abstractions.IMessageSinkMessage})">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DelegatingMessageBus"/> class.
            </summary>
            <param name="innerMessageBus">The message bus to delegate to.</param>
            <param name="callback">The callback to send messages to.</param>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageBus.QueueMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageBus.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.DelegatingMessageBus`1">
            <summary>
            Implementation of <see cref="T:Xunit.Sdk.IMessageBus" /> that delegates to another implementation of
            <see cref="T:Xunit.Sdk.IMessageBus" /> while calling into a callback for each message. In addition,
            it issues a <see cref="P:Xunit.Sdk.DelegatingMessageBus`1.Finished" /> event when a message of the type <typeparamref name="TFinalMessage"/>
            is seen.
            </summary>
            <typeparam name="TFinalMessage">The type of the T final message.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageBus`1.#ctor(Xunit.Sdk.IMessageBus,System.Action{Xunit.Abstractions.IMessageSinkMessage})">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DelegatingMessageSink`1" /> class.
            </summary>
            <param name="innerMessageBus">The message bus to delegate to.</param>
            <param name="callback">The callback to send messages to.</param>
        </member>
        <member name="P:Xunit.Sdk.DelegatingMessageBus`1.FinalMessage">
            <summary>
            The final message that was seen that caused <see cref="P:Xunit.Sdk.DelegatingMessageBus`1.Finished"/> to be triggered.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.DelegatingMessageBus`1.Finished">
            <summary>
            An event that is triggered when a message of type <typeparamref name="TFinalMessage" /> is seen.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageBus`1.QueueMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.DelegatingMessageSink">
            <summary>
            Implementation of <see cref="T:Xunit.Abstractions.IMessageSink" /> that delegates to another implementation of
            <see cref="T:Xunit.Abstractions.IMessageSink" /> while calling into a callback for each message.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageSink.#ctor(Xunit.Abstractions.IMessageSink,System.Action{Xunit.Abstractions.IMessageSinkMessage})">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DelegatingMessageSink"/> class.
            </summary>
            <param name="innerSink">The inner message sink.</param>
            <param name="callback">The callback.</param>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageSink.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageSink.OnMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.DelegatingMessageSink`1">
            <summary>
            Implementation of <see cref="T:Xunit.Abstractions.IMessageSink" /> that delegates to another implementation of
            <see cref="T:Xunit.Abstractions.IMessageSink" /> while calling into a callback for each message. In addition,
            it issues a <see cref="P:Xunit.Sdk.DelegatingMessageSink`1.Finished" /> event when a message of the type <typeparamref name="TFinalMessage"/>
            is seen.
            </summary>
            <typeparam name="TFinalMessage">The type of the T final message.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageSink`1.#ctor(Xunit.Abstractions.IMessageSink,System.Action{Xunit.Abstractions.IMessageSinkMessage})">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DelegatingMessageSink" /> class.
            </summary>
            <param name="innerSink">The inner message sink.</param>
            <param name="callback">The callback.</param>
        </member>
        <member name="P:Xunit.Sdk.DelegatingMessageSink`1.FinalMessage">
            <summary>
            The final message that was seen that caused <see cref="P:Xunit.Sdk.DelegatingMessageSink`1.Finished"/> to be triggered.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.DelegatingMessageSink`1.Finished">
            <summary>
            An event that is triggered when a message of type <typeparamref name="TFinalMessage" /> is seen.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DelegatingMessageSink`1.OnMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.ReflectionAssemblyInfo">
            <summary>
            Reflection-based implementation of <see cref="T:Xunit.Abstractions.IReflectionAssemblyInfo"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAssemblyInfo.#ctor(System.Reflection.Assembly)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ReflectionAssemblyInfo"/> class.
            </summary>
            <param name="assembly">The assembly to be wrapped.</param>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAssemblyInfo.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ReflectionAssemblyInfo"/> class.
            </summary>
            <param name="assemblyFileName">The assembly to be wrapped.</param>
        </member>
        <member name="P:Xunit.Sdk.ReflectionAssemblyInfo.Assembly">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionAssemblyInfo.AssemblyPath">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionAssemblyInfo.Name">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAssemblyInfo.GetCustomAttributes(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAssemblyInfo.GetType(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAssemblyInfo.GetTypes(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAssemblyInfo.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.ReflectionAttributeInfo">
            <summary>
            Reflection-based implementation of <see cref="T:Xunit.Abstractions.IReflectionAttributeInfo"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAttributeInfo.#ctor(System.Reflection.CustomAttributeData)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ReflectionAttributeInfo"/> class.
            </summary>
            <param name="attribute">The attribute to be wrapped.</param>
        </member>
        <member name="P:Xunit.Sdk.ReflectionAttributeInfo.Attribute">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionAttributeInfo.AttributeData">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAttributeInfo.GetConstructorArguments">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAttributeInfo.GetCustomAttributes(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAttributeInfo.GetNamedArgument``1(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionAttributeInfo.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.ReflectionMethodInfo">
            <summary>
            Reflection-based implementation of <see cref="T:Xunit.Abstractions.IReflectionMethodInfo"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ReflectionMethodInfo.#ctor(System.Reflection.MethodInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ReflectionMethodInfo"/> class.
            </summary>
            <param name="method">The method to be wrapped.</param>
        </member>
        <member name="P:Xunit.Sdk.ReflectionMethodInfo.IsAbstract">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionMethodInfo.IsGenericMethodDefinition">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionMethodInfo.IsPublic">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionMethodInfo.IsStatic">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionMethodInfo.MethodInfo">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionMethodInfo.Name">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionMethodInfo.ReturnType">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionMethodInfo.Type">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionMethodInfo.GetCustomAttributes(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionMethodInfo.GetGenericArguments">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionMethodInfo.MakeGenericMethod(Xunit.Abstractions.ITypeInfo[])">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionMethodInfo.ToString">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionMethodInfo.GetParameters">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.ReflectionParameterInfo">
            <summary>
            Reflection-based implementation of <see cref="T:Xunit.Abstractions.IReflectionParameterInfo"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ReflectionParameterInfo.#ctor(System.Reflection.ParameterInfo)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ReflectionParameterInfo"/> class.
            </summary>
            <param name="parameterInfo">The parameter to be wrapped.</param>
        </member>
        <member name="P:Xunit.Sdk.ReflectionParameterInfo.Name">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionParameterInfo.ParameterInfo">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionParameterInfo.ParameterType">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.ReflectionTypeInfo">
            <summary>
            Reflection-based implementation of <see cref="T:Xunit.Abstractions.IReflectionTypeInfo"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ReflectionTypeInfo.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ReflectionTypeInfo"/> class.
            </summary>
            <param name="type">The type to wrap.</param>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.Assembly">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.BaseType">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.Interfaces">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.IsAbstract">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.IsGenericParameter">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.IsGenericType">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.IsSealed">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.IsValueType">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.Name">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ReflectionTypeInfo.Type">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionTypeInfo.GetCustomAttributes(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionTypeInfo.GetGenericArguments">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionTypeInfo.GetMethod(System.String,System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionTypeInfo.GetMethods(System.Boolean)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.ReflectionTypeInfo.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.Reflector">
            <summary>
            Wrapper to implement types from xunit.abstractions.dll using reflection.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.Reflector.ConvertArguments(System.Object[],System.Type[])">
            <summary>
            Converts arguments into their target types. Can be particularly useful when pulling attribute
            constructor arguments, whose types may not strictly match the parameter types.
            </summary>
            <param name="args">The arguments to be converted.</param>
            <param name="types">The target types for the conversion.</param>
            <returns>The converted arguments.</returns>
        </member>
        <member name="M:Xunit.Sdk.Reflector.Wrap(System.Reflection.Assembly)">
            <summary>
            Converts an <see cref="T:System.Reflection.Assembly"/> into an <see cref="T:Xunit.Abstractions.IReflectionAssemblyInfo"/>.
            </summary>
            <param name="assembly">The assembly to wrap.</param>
            <returns>The wrapper</returns>
        </member>
        <member name="M:Xunit.Sdk.Reflector.Wrap(System.Reflection.CustomAttributeData)">
            <summary>
            Converts an <see cref="T:System.Attribute"/> into an <see cref="T:Xunit.Abstractions.IAttributeInfo"/> using reflection.
            </summary>
            <param name="attribute">The attribute to wrap.</param>
            <returns>The wrapper</returns>
        </member>
        <member name="M:Xunit.Sdk.Reflector.Wrap(System.Reflection.MethodInfo)">
            <summary>
            Converts a <see cref="T:System.Reflection.MethodInfo"/> into an <see cref="T:Xunit.Abstractions.IMethodInfo"/> using reflection.
            </summary>
            <param name="method">The method to wrap</param>
            <returns>The wrapper</returns>
        </member>
        <member name="M:Xunit.Sdk.Reflector.Wrap(System.Reflection.ParameterInfo)">
            <summary>
            Converts a <see cref="T:System.Reflection.ParameterInfo"/> into an <see cref="T:Xunit.Abstractions.IParameterInfo"/> using reflection.
            </summary>
            <param name="parameter">THe parameter to wrap</param>
            <returns>The wrapper</returns>
        </member>
        <member name="M:Xunit.Sdk.Reflector.Wrap(System.Type)">
            <summary>
            Converts a <see cref="T:System.Type"/> into an <see cref="T:Xunit.Abstractions.ITypeInfo"/> using reflection.
            </summary>
            <param name="type">The type to wrap</param>
            <returns>The wrapper</returns>
        </member>
        <member name="T:Xunit.Sdk.SynchronousMessageBus">
            <summary>
            This is an internal class, and is not intended to be called from end-user code.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.SynchronousMessageBus.#ctor(Xunit.Abstractions.IMessageSink,System.Boolean)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.SynchronousMessageBus.Dispose">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.SynchronousMessageBus.QueueMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.TestCaseBulkDeserializer">
            <summary>
            INTERNAL CLASS. DO NOT USE.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseBulkDeserializer.#ctor(System.Object,System.Object,System.Object,System.Object)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.TestCaseDescriptorFactory">
            <summary>
            INTERNAL CLASS. DO NOT USE.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseDescriptorFactory.#ctor(System.Object,System.Object,System.Object)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.TestClassException">
            <summary>
            Represents an exception that happened during the process of a test class. This typically
            means there were problems identifying the correct test class constructor, or problems
            creating the fixture data for the test class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:Xunit.Sdk.TestClassException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestDiscoveryVisitor.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.TestDiscoveryVisitor.OnMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestFrameworkTypeDiscoverer">
            <summary>
            The implementation of <see cref="T:Xunit.Sdk.ITestFrameworkTypeDiscoverer"/> that supports attributes
            of type <see cref="T:Xunit.Sdk.TestFrameworkDiscovererAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkTypeDiscoverer.GetTestFrameworkType(Xunit.Abstractions.IAttributeInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TraitHelper">
            <summary>
            A helper class to retrieve the traits from a method.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TraitHelper.GetTraits(System.Reflection.MemberInfo)">
            <summary>
            Get the traits from a method.
            </summary>
            <param name="member">The member (method, field, etc.) to get the traits for.</param>
            <returns>A list of traits that are defined on the method.</returns>
        </member>
        <member name="T:Xunit.Sdk.TypeUtility">
            <summary>
            Extension methods for <see cref="T:Xunit.Abstractions.ITypeInfo"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TypeUtility.ConvertToSimpleTypeName(Xunit.Abstractions.ITypeInfo)">
            <summary>
            Converts a type into a name string.
            </summary>
            <param name="type">The type to convert.</param>
            <returns>Name string of type.</returns>
        </member>
        <member name="M:Xunit.Sdk.TypeUtility.ResolveMethodArguments(System.Reflection.MethodBase,System.Object[])">
            <summary>
            Resolves argument values for the test method, including support for optional method
            arguments.
            </summary>
            <param name="testMethod">The test method to resolve.</param>
            <param name="arguments">The user-supplied method arguments.</param>
            <returns>The argument values</returns>
        </member>
        <member name="M:Xunit.Sdk.TypeUtility.GetDisplayNameWithArguments(Xunit.Abstractions.IMethodInfo,System.String,System.Object[],Xunit.Abstractions.ITypeInfo[])">
            <summary>
            Formulates the extended portion of the display name for a test method. For tests with no arguments, this will
            return just the base name; for tests with arguments, attempts to format the arguments and appends the argument
            list to the test name.
            </summary>
            <param name="method">The test method</param>
            <param name="baseDisplayName">The base part of the display name</param>
            <param name="arguments">The test method arguments</param>
            <param name="genericTypes">The test method's generic types</param>
            <returns>The full display name for the test method</returns>
        </member>
        <member name="M:Xunit.Sdk.TypeUtility.ResolveGenericParameter(Xunit.Abstractions.ITypeInfo,Xunit.Abstractions.ITypeInfo,System.Type,System.Type@)">
            <summary>
            Resolves an individual generic type given an intended generic parameter type and the type of an object passed to that type.
            </summary>
            <param name="genericType">The generic type, e.g. T, to resolve.</param>
            <param name="methodParameterType">The non-generic or open generic type, e.g. T, to try to match with the type of the object passed to that type.</param>
            <param name="passedParameterType">The non-generic or closed generic type, e.g. string, used to resolve the method parameter.</param>
            <param name="resultType">The resolved type, e.g. the parameters (T, T, string, typeof(object)) -> (T, T, string, typeof(string)).</param>
            <returns>True if resolving was successful, else false.</returns>
        </member>
        <member name="M:Xunit.Sdk.TypeUtility.GetArrayElementTypeOrThis(System.Type)">
            <summary>
            Gets the ElementType of a type, only if it is an array.
            </summary>
            <param name="type">The type to get the ElementType of.</param>
            <returns>If type is an array, the ElementType of the type, else the original type.</returns>
        </member>
        <member name="M:Xunit.Sdk.TypeUtility.StripElementType(Xunit.Abstractions.ITypeInfo,System.Boolean@)">
            <summary>
            Gets the underlying ElementType of a type, if the ITypeInfo supports reflection.
            </summary>
            <param name="type">The type to get the ElementType of.</param>
            <param name="isArray">A flag indicating whether the type is an array.</param>
            <returns>If type has an element type, underlying ElementType of a type, else the original type.</returns>
        </member>
        <member name="M:Xunit.Sdk.TypeUtility.ResolveMismatchedGenericArguments(Xunit.Abstractions.ITypeInfo,System.Type,Xunit.Abstractions.ITypeInfo[],System.Type@)">
            <summary>
            Resolves an individual generic type given an intended generic parameter type and the type of an object passed to that type.
            </summary>
            <param name="genericType">The generic type, e.g. T, to resolve.</param>
            <param name="passedParameterType">The non-generic or closed generic type, e.g. string, used to resolve the method parameter.</param>
            <param name="methodGenericTypeArguments">The generic arguments of the open generic type to match with the passed parameter.</param>
            <param name="resultType">The resolved type.</param>
            <returns>True if resolving was successful, else false.</returns>
        </member>
        <member name="M:Xunit.Sdk.TypeUtility.ResolveGenericType(Xunit.Abstractions.ITypeInfo,System.Object[],Xunit.Abstractions.IParameterInfo[])">
            <summary>
            Resolves a generic type for a test method. The test parameters (and associated parameter infos) are
            used to determine the best matching generic type for the test method that can be satisfied by all
            the generic parameters and their values.
            </summary>
            <param name="genericType">The generic type to be resolved</param>
            <param name="parameters">The parameter values being passed to the test method</param>
            <param name="parameterInfos">The parameter infos for the test method</param>
            <returns>The best matching generic type</returns>
        </member>
        <member name="M:Xunit.Sdk.TypeUtility.ResolveGenericTypes(Xunit.Abstractions.IMethodInfo,System.Object[])">
            <summary>
            Resolves all the generic types for a test method. The test parameters are used to determine
            the best matching generic types for the test method that can be satisfied by all
            the generic parameters and their values.
            </summary>
            <param name="method">The test method</param>
            <param name="parameters">The parameter values being passed to the test method</param>
            <returns>The best matching generic types</returns>
        </member>
        <member name="T:Xunit.Sdk.GeneralDigest">
            base implementation of MD4 family style digest as outlined in
            "Handbook of Applied Cryptography", pages 344 - 347.
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.GeneralDigest.CopyIn(Xunit.Sdk.GeneralDigest)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.GeneralDigest.Update(System.Byte)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.GeneralDigest.BlockUpdate(System.Byte[],System.Int32,System.Int32)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.GeneralDigest.Finish">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.GeneralDigest.Reset">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.GeneralDigest.GetByteLength">
            <summary/>
        </member>
        <member name="P:Xunit.Sdk.GeneralDigest.AlgorithmName">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.GeneralDigest.GetDigestSize">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.GeneralDigest.DoFinal(System.Byte[],System.Int32)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.Sha1Digest">
             implementation of SHA-1 as outlined in "Handbook of Applied Cryptography", pages 346 - 349.
            
             It is interesting to ponder why the, apart from the extra IV, the other difference here from MD5
             is the "endianness" of the word processing!
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.Sha1Digest.#ctor">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.Sha1Digest.#ctor(Xunit.Sdk.Sha1Digest)">
            Copy constructor.  This will copy the state of the provided
            message digest.
        </member>
        <member name="P:Xunit.Sdk.Sha1Digest.AlgorithmName">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.Sha1Digest.GetDigestSize">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.Sha1Digest.DoFinal(System.Byte[],System.Int32)">
            <summary/>
        </member>
        <member name="M:Xunit.Sdk.Sha1Digest.Reset">
            reset the chaining variables
        </member>
        <member name="M:Xunit.Sdk.Sha1Digest.Reset(Xunit.Sdk.Sha1Digest)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.ExceptionUtility">
            <summary>
            Utility classes for dealing with Exception objects.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExceptionUtility.CombineMessages(Xunit.Abstractions.IFailureInformation)">
            <summary>
            Combines multiple levels of messages into a single message.
            </summary>
            <param name="failureInfo">The failure information from which to get the messages.</param>
            <returns>The combined string.</returns>
        </member>
        <member name="M:Xunit.Sdk.ExceptionUtility.CombineStackTraces(Xunit.Abstractions.IFailureInformation)">
            <summary>
            Combines multiple levels of stack traces into a single stack trace.
            </summary>
            <param name="failureInfo">The failure information from which to get the stack traces.</param>
            <returns>The combined string.</returns>
        </member>
        <member name="M:Xunit.Sdk.ExceptionUtility.ConvertExceptionToFailureInformation(System.Exception)">
            <summary>
            Unwraps exceptions and their inner exceptions.
            </summary>
            <param name="ex">The exception to be converted.</param>
            <returns>The failure information.</returns>
        </member>
        <member name="T:Xunit.Sdk.NullMessageSink">
            <summary>
            An implementation of <see cref="T:Xunit.Abstractions.IMessageSink"/> and <see cref="T:Xunit.IMessageSinkWithTypes"/> that
            ignores all messages.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.NullMessageSink.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.NullMessageSink.OnMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.NullMessageSink.OnMessageWithTypes(Xunit.Abstractions.IMessageSinkMessage,System.Collections.Generic.HashSet{System.String})">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.SerializationHelper">
            <summary>
            Serializes and de-serializes objects. Serialization of objects is typically limited to supporting
            the VSTest-based test runners (Visual Studio Test Explorer, Visual Studio Code, dotnet test, etc.).
            Serializing values with this is not guaranteed to be cross-compatible and should not be used for
            anything durable.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.SerializationHelper.Deserialize``1(System.String)">
            <summary>
            De-serializes an object.
            </summary>
            <typeparam name="T">The type of the object</typeparam>
            <param name="serializedValue">The object's serialized value</param>
            <returns>The de-serialized object</returns>
        </member>
        <member name="M:Xunit.Sdk.SerializationHelper.GetType(System.String)">
            <summary>
            Converts an assembly qualified type name into a <see cref="T:System.Type"/> object.
            </summary>
            <param name="assemblyQualifiedTypeName">The assembly qualified type name.</param>
            <returns>The instance of the <see cref="T:System.Type"/>, if available; <c>null</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.SerializationHelper.GetType(System.String,System.String)">
            <summary>
            Converts an assembly name + type name into a <see cref="T:System.Type"/> object.
            </summary>
            <param name="assemblyName">The assembly name.</param>
            <param name="typeName">The type name.</param>
            <returns>The instance of the <see cref="T:System.Type"/>, if available; <c>null</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.SerializationHelper.GetTypeNameForSerialization(System.Type)">
            <summary>
            Gets an assembly qualified type name for serialization, with special handling for types which
            live in assembly decorated by <see cref="T:Xunit.Sdk.PlatformSpecificAssemblyAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.SerializationHelper.IsSerializable(System.Object)">
            <summary>
            Determines whether the given <paramref name="value"/> is serializable with <see cref="M:Xunit.Sdk.SerializationHelper.Serialize(System.Object)"/>.
            </summary>
            <param name="value">The object to test for serializability.</param>
            <returns>Returns <c>true</c> if the object can be serialized; <c>false</c>, otherwise.</returns>
        </member>
        <member name="M:Xunit.Sdk.SerializationHelper.Serialize(System.Object)">
            <summary>
            Serializes an object.
            </summary>
            <param name="value">The value to serialize</param>
            <returns>The serialized value</returns>
        </member>
        <member name="T:Xunit.Sdk.SourceInformation">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ISourceInformation"/>.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.SourceInformation.FileName">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.SourceInformation.LineNumber">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.SourceInformation.Serialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.SourceInformation.Deserialize(Xunit.Abstractions.IXunitSerializationInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.AfterTestFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.IAfterTestFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.AfterTestFinished.#ctor(Xunit.Abstractions.ITest,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.AfterTestFinished"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.AfterTestFinished.AttributeName">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.AfterTestStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.IAfterTestStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.AfterTestStarting.#ctor(Xunit.Abstractions.ITest,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.AfterTestStarting"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.AfterTestStarting.AttributeName">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestAssemblyMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestAssemblyMessage"/> and <see cref="T:Xunit.Abstractions.IExecutionMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyMessage.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestAssembly)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestAssemblyMessage"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyMessage.#ctor(Xunit.Abstractions.ITestCase,Xunit.Abstractions.ITestAssembly)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestAssemblyMessage"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyMessage.TestAssembly">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyMessage.TestCases">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCaseMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCaseMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseMessage.#ctor(Xunit.Abstractions.ITestCase)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCaseMessage"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCaseMessage.TestCase">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestClassMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestClassMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassMessage.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestClass)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassMessage"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassMessage.#ctor(Xunit.Abstractions.ITestCase,Xunit.Abstractions.ITestClass)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassMessage"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassMessage.TestClass">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCollectionMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCollectionMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionMessage.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCollectionMessage"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionMessage.#ctor(Xunit.Abstractions.ITestCase,Xunit.Abstractions.ITestCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCollectionMessage"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionMessage.TestCollection">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMessage.#ctor(Xunit.Abstractions.ITest)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestMessage"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMessage.Test">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestMethodMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestMethodMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodMessage.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestMethod)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestMethodMessage"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodMessage.#ctor(Xunit.Abstractions.ITestCase,Xunit.Abstractions.ITestMethod)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestMethodMessage"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodMessage.TestMethod">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestResultMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestResultMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestResultMessage.#ctor(Xunit.Abstractions.ITest,System.Decimal,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestResultMessage"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestResultMessage.ExecutionTime">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestResultMessage.Output">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.BeforeTestFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.IBeforeTestFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.BeforeTestFinished.#ctor(Xunit.Abstractions.ITest,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.BeforeTestFinished"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.BeforeTestFinished.AttributeName">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.BeforeTestStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.IBeforeTestStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.BeforeTestStarting.#ctor(Xunit.Abstractions.ITest,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.BeforeTestStarting"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.BeforeTestStarting.AttributeName">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.DiagnosticMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.IDiagnosticMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DiagnosticMessage.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DiagnosticMessage"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DiagnosticMessage.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DiagnosticMessage"/> class.
            </summary>
            <param name="message">The message to send</param>
        </member>
        <member name="M:Xunit.Sdk.DiagnosticMessage.#ctor(System.String,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.DiagnosticMessage"/> class.
            </summary>
            <param name="format">The format of the message to send</param>
            <param name="args">The arguments used to format the message</param>
        </member>
        <member name="P:Xunit.Sdk.DiagnosticMessage.InterfaceTypes">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.DiagnosticMessage.Message">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.DiagnosticMessage.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.DiscoveryCompleteMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.IDiscoveryCompleteMessage"/>.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.ErrorMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.IErrorMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ErrorMessage.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},System.String[],System.String[],System.String[],System.Int32[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ErrorMessage"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ErrorMessage.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ErrorMessage"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.ErrorMessage.ExceptionTypes">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ErrorMessage.Messages">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ErrorMessage.StackTraces">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ErrorMessage.ExceptionParentIndices">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.ErrorMessage.TestCases">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestAssemblyCleanupFailure">
            <summary>
            Default implementation of <see cref="T:Xunit.Sdk.TestAssemblyCleanupFailure"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyCleanupFailure.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestAssembly,System.String[],System.String[],System.String[],System.Int32[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestAssemblyCleanupFailure"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyCleanupFailure.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestAssembly,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestAssemblyCleanupFailure"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyCleanupFailure.ExceptionParentIndices">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyCleanupFailure.ExceptionTypes">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyCleanupFailure.Messages">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyCleanupFailure.StackTraces">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestAssemblyFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestAssemblyFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyFinished.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestAssembly,System.Decimal,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestAssemblyFinished"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyFinished.ExecutionTime">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyFinished.TestsFailed">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyFinished.TestsRun">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyFinished.TestsSkipped">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestAssemblyStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestAssemblyStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestAssemblyStarting.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestAssembly,System.DateTime,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestAssemblyStarting"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyStarting.StartTime">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyStarting.TestEnvironment">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestAssemblyStarting.TestFrameworkDisplayName">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCaseCleanupFailure">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCaseCleanupFailure"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseCleanupFailure.#ctor(Xunit.Abstractions.ITestCase,System.String[],System.String[],System.String[],System.Int32[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCaseCleanupFailure"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseCleanupFailure.#ctor(Xunit.Abstractions.ITestCase,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCaseCleanupFailure"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCaseCleanupFailure.ExceptionTypes">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCaseCleanupFailure.Messages">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCaseCleanupFailure.StackTraces">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCaseCleanupFailure.ExceptionParentIndices">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCaseDiscoveryMessage">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCaseDiscoveryMessage"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseDiscoveryMessage.#ctor(Xunit.Abstractions.ITestCase)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCaseDiscoveryMessage"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestCaseFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCaseFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseFinished.#ctor(Xunit.Abstractions.ITestCase,System.Decimal,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCaseFinished"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCaseFinished.ExecutionTime">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCaseFinished.TestsFailed">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCaseFinished.TestsRun">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCaseFinished.TestsSkipped">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCaseStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCaseStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCaseStarting.#ctor(Xunit.Abstractions.ITestCase)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCaseStarting"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestClassCleanupFailure">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestClassCleanupFailure"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassCleanupFailure.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestClass,System.String[],System.String[],System.String[],System.Int32[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassCleanupFailure"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassCleanupFailure.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestClass,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassCleanupFailure"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassCleanupFailure.ExceptionTypes">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestClassCleanupFailure.Messages">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestClassCleanupFailure.StackTraces">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestClassCleanupFailure.ExceptionParentIndices">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestClassConstructionFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestClassConstructionFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassConstructionFinished.#ctor(Xunit.Abstractions.ITest)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassConstructionFinished"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestClassConstructionStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestClassConstructionStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassConstructionStarting.#ctor(Xunit.Abstractions.ITest)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassConstructionStarting"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestClassDisposeFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestClassDisposeFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassDisposeFinished.#ctor(Xunit.Abstractions.ITest)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassDisposeFinished"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestClassDisposeStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestClassDisposeStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassDisposeStarting.#ctor(Xunit.Abstractions.ITest)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassDisposeStarting"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestClassFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestClassFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassFinished.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestClass,System.Decimal,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassFinished"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestClassFinished.ExecutionTime">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestClassFinished.TestsFailed">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestClassFinished.TestsRun">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestClassFinished.TestsSkipped">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestClassStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestClassStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestClassStarting.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestClass)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassStarting"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestCleanupFailure">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCleanupFailure"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCleanupFailure.#ctor(Xunit.Abstractions.ITest,System.String[],System.String[],System.String[],System.Int32[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCleanupFailure"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCleanupFailure.#ctor(Xunit.Abstractions.ITest,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCleanupFailure"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCleanupFailure.ExceptionTypes">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCleanupFailure.Messages">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCleanupFailure.StackTraces">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCleanupFailure.ExceptionParentIndices">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCollectionCleanupFailure">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCollectionCleanupFailure"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionCleanupFailure.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestCollection,System.String[],System.String[],System.String[],System.Int32[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassCleanupFailure"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionCleanupFailure.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestCollection,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassCleanupFailure"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionCleanupFailure.ExceptionTypes">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionCleanupFailure.Messages">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionCleanupFailure.StackTraces">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionCleanupFailure.ExceptionParentIndices">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCollectionFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCollectionFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionFinished.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestCollection,System.Decimal,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCollectionFinished"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionFinished.ExecutionTime">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionFinished.TestsFailed">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionFinished.TestsRun">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestCollectionFinished.TestsSkipped">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestCollectionStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestCollectionStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestCollectionStarting.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestCollectionStarting"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestFailed">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestFailed"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestFailed.#ctor(Xunit.Abstractions.ITest,System.Decimal,System.String,System.String[],System.String[],System.String[],System.Int32[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestFailed"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestFailed.#ctor(Xunit.Abstractions.ITest,System.Decimal,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestFailed"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFailed.ExceptionTypes">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestFailed.Messages">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestFailed.StackTraces">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestFailed.ExceptionParentIndices">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestFinished.#ctor(Xunit.Abstractions.ITest,System.Decimal,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestFinished"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestFinished.ExecutionTime">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestFinished.Output">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestMethodCleanupFailure">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestMethodCleanupFailure"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodCleanupFailure.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestMethod,System.String[],System.String[],System.String[],System.Int32[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassCleanupFailure"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodCleanupFailure.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestMethod,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestClassCleanupFailure"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodCleanupFailure.ExceptionTypes">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodCleanupFailure.Messages">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodCleanupFailure.StackTraces">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodCleanupFailure.ExceptionParentIndices">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestMethodFinished">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestMethodFinished"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodFinished.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestMethod,System.Decimal,System.Int32,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestMethodFinished"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestMethodFinished.ExecutionTime">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodFinished.TestsFailed">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodFinished.TestsRun">
            <inheritdoc/>
        </member>
        <member name="P:Xunit.Sdk.TestMethodFinished.TestsSkipped">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestMethodStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestMethodStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestMethodStarting.#ctor(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCase},Xunit.Abstractions.ITestMethod)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestMethodStarting"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestOutput">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestOutput"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestOutput.#ctor(Xunit.Abstractions.ITest,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestOutput"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestOutput.Output">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestPassed">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestPassed"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestPassed.#ctor(Xunit.Abstractions.ITest,System.Decimal,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestPassed"/> class.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestSkipped">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestSkipped"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestSkipped.#ctor(Xunit.Abstractions.ITest,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestSkipped"/> class.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.TestSkipped.Reason">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TestStarting">
            <summary>
            Default implementation of <see cref="T:Xunit.Abstractions.ITestStarting"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestStarting.#ctor(Xunit.Abstractions.ITest)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.TestStarting"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer.GetDefaultComparer(System.Type)">
            <summary>
            Gets the default comparer to be used for the provided <paramref name="type"/> when a custom one
            has not been provided. Creates an instance of <see cref="T:Xunit.Sdk.AssertEqualityComparer`1"/> wrapped
            by <see cref="T:Xunit.Sdk.AssertEqualityComparerAdapter`1"/>.
            </summary>
            <param name="type">The type to be compared</param>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer.GetDefaultInnerComparer(System.Type)">
            <summary>
            Gets the default comparer to be used as an inner comparer for the provided <paramref name="type"/>
            when a custom one has not been provided. For non-collections, this defaults to an <see cref="T:System.Object"/>-based
            comparer; for collections, this creates an inner comparer based on the item type in the collection.
            </summary>
            <param name="type">The type to create an inner comparer for</param>
        </member>
        <member name="T:Xunit.Sdk.AssertEqualityComparer.OperationalFailureException">
            <summary>
            This exception is thrown when an operation failure has occured during equality comparison operations.
            This generally indicates that a necessary pre-condition was not met for comparison operations to succeed.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer.OperationalFailureException.ForIllegalGetHashCode">
            <summary>
            Gets an exception that indicates that GetHashCode was called on <see cref="T:Xunit.Sdk.AssertEqualityComparer`1.FuncEqualityComparer"/>
            which usually indicates that an item comparison function was used to try to compare two hash sets.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.AssertEqualityComparer`1">
            <summary>
            Default implementation of <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> used by the xUnit.net equality assertions
            (except for collections, which are handled directly by the appropriate assertion methods).
            </summary>
            <typeparam name="T">The type that is being compared.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer`1.#ctor(System.Collections.IEqualityComparer)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.AssertEqualityComparer`1" /> class.
            </summary>
            <param name="innerComparer">The inner comparer to be used when the compared objects are enumerable.</param>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer`1.Equals(`0,`0)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer`1.GetHashCode(`0)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparer`1.GuardArgumentNotNull``1(System.String,``0)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.AssertEqualityComparerAdapter`1">
            <summary>
            A class that wraps <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> to add <see cref="T:System.Collections.IEqualityComparer"/>.
            </summary>
            <typeparam name="T">The type that is being compared.</typeparam>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.AssertEqualityComparerAdapter`1"/> class.
            </summary>
            <param name="innerComparer">The comparer that is being adapted.</param>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.Equals(System.Object,System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.Equals(`0,`0)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.GetHashCode(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.AssertEqualityComparerAdapter`1.GetHashCode(`0)">
            <inheritdoc/>
        </member>
        <member name="F:Xunit.ExecutionHelper.SubstitutionToken">
            <summary>
            Gets the substitution token used as assembly name suffix to indicate that the assembly is
            a generalized reference to the platform-specific assembly.
            </summary>
        </member>
        <member name="T:Xunit.LongLivedMarshalByRefObject">
            <summary>
            Base class for all long-lived objects that may cross over an AppDomain.
            </summary>
        </member>
        <member name="M:Xunit.LongLivedMarshalByRefObject.#ctor">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.LongLivedMarshalByRefObject"/> type.
            </summary>
        </member>
        <member name="M:Xunit.LongLivedMarshalByRefObject.DisconnectAll">
            <summary>
            Disconnects all remote objects.
            </summary>
        </member>
        <member name="M:Xunit.LongLivedMarshalByRefObject.InitializeLifetimeService">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Serialization.XunitSerializationInfo">
            <summary>
            A mirror class of the CLR's <see cref="T:System.Runtime.Serialization.SerializationInfo"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationInfo.#ctor(Xunit.Abstractions.IXunitSerializable)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Serialization.XunitSerializationInfo"/> class.
            </summary>
            <param name="object">The data to copy into the serialization info</param>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationInfo.AddValue(System.String,System.Object,System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationInfo.Deserialize(System.Type,System.String)">
            <summary>
            De-serializes a value that was serialized with <see cref="T:Xunit.Serialization.XunitSerializationInfo"/>.
            </summary>
            <param name="type">The type of the object to de-serialize into</param>
            <param name="serializedValue">The serialized value</param>
            <returns>The de-serialized object</returns>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationInfo.DeserializeTriple(System.String)">
            <summary>
            Returns the triple values out of a serialized triple.
            </summary>
            <param name="value">The serialized triple</param>
            <returns>The de-serialized triple</returns>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationInfo.GetValue``1(System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationInfo.GetValue(System.String,System.Type)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationInfo.Serialize(System.Object)">
            <summary>
            Serializes an object.
            </summary>
            <param name="value">The value to be serialized</param>
            <returns>The serialized object</returns>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationInfo.SerializeTriple(Xunit.Serialization.XunitSerializationTriple)">
            <summary>
            Returns a triple for a key/value pair of data in a complex object
            </summary>
            <param name="triple">The triple to be serialized</param>
            <returns>The serialized version of the triple</returns>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationInfo.ToSerializedString">
            <summary>
            Returns BASE64 encoded string that represents the entirety of the data.
            </summary>
        </member>
        <member name="T:Xunit.Serialization.XunitSerializationTriple">
            <summary>
            Represents a triple of information used when serializing complex types: the property name,
            the value to be serialized, and the value's type.
            </summary>
        </member>
        <member name="F:Xunit.Serialization.XunitSerializationTriple.Key">
            <summary>
            Gets the triple's key
            </summary>
        </member>
        <member name="F:Xunit.Serialization.XunitSerializationTriple.Value">
            <summary>
            Gets the triple's value
            </summary>
        </member>
        <member name="F:Xunit.Serialization.XunitSerializationTriple.Type">
            <summary>
            Gets the triple's value type
            </summary>
        </member>
        <member name="M:Xunit.Serialization.XunitSerializationTriple.#ctor(System.String,System.Object,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Serialization.XunitSerializationTriple"/> class.
            </summary>
            <param name="key">The triple's key</param>
            <param name="value">The triple's value</param>
            <param name="type">The triple's value type</param>
        </member>
        <member name="T:Xunit.Internal.ArgumentFormatter">
            <summary>
            Formats value for display in assertion messages and data-driven test display names.
            </summary>
        </member>
        <member name="F:Xunit.Internal.ArgumentFormatter.MAX_DEPTH">
            <summary>
            Gets the maximum printing depth, in terms of objects before truncation.
            </summary>
        </member>
        <member name="F:Xunit.Internal.ArgumentFormatter.MAX_ENUMERABLE_LENGTH">
            <summary>
            Gets the maximum number of values printed for collections before truncation.
            </summary>
        </member>
        <member name="F:Xunit.Internal.ArgumentFormatter.MAX_OBJECT_ITEM_COUNT">
            <summary>
            Gets the maximum number of items (properties or fields) printed in an object before truncation.
            </summary>
        </member>
        <member name="F:Xunit.Internal.ArgumentFormatter.MAX_STRING_LENGTH">
            <summary>
            Gets the maximum strength length before truncation.
            </summary>
        </member>
        <member name="P:Xunit.Internal.ArgumentFormatter.Ellipsis">
            <summary>
            Gets the ellipsis value (three middle dots, aka U+00B7).
            </summary>
        </member>
        <member name="M:Xunit.Internal.ArgumentFormatter.EscapeString(System.String)">
            <summary>
            Escapes a string for printing, attempting to most closely model the value on how you would
            enter the value in a C# string literal. That means control codes that are normally backslash
            escaped (like "\n" for newline) are represented like that; all other control codes for ASCII
            values under 32 are printed as "\xnn".
            </summary>
            <param name="s">The string value to be escaped</param>
        </member>
        <member name="M:Xunit.Internal.ArgumentFormatter.Format(System.Object,System.Int32)">
            <summary>
            Formats a value for display.
            </summary>
            <param name="value">The value to be formatted</param>
            <param name="depth">The optional printing depth (1 indicates a top-level value)</param>
        </member>
        <member name="M:Xunit.Internal.ArgumentFormatter.FormatTypeName(System.Type,System.Boolean)">
            <summary>
            Formats a type. This maps built-in C# types to their C# native name (e.g., printing "int" instead
            of "Int32" or "System.Int32").
            </summary>
            <param name="type">The type to get the formatted name of</param>
            <param name="fullTypeName">Set to <c>true</c> to include the namespace; set to <c>false</c> for just the simple type name</param>
        </member>
        <member name="M:ExceptionExtensions.RethrowWithNoStackTraceLoss(System.Exception)">
            <summary>
            Rethrows an exception object without losing the existing stack trace information
            </summary>
            <param name="ex">The exception to re-throw.</param>
            <remarks>
            For more information on this technique, see
            http://www.dotnetjunkies.com/WebLog/chris.taylor/archive/2004/03/03/8353.aspx.
            The remote_stack_trace string is here to support Mono.
            </remarks>
        </member>
        <member name="M:ExceptionExtensions.Unwrap(System.Exception)">
            <summary>
            Unwraps an exception to remove any wrappers, like <see cref="T:System.Reflection.TargetInvocationException"/>.
            </summary>
            <param name="ex">The exception to unwrap.</param>
            <returns>The unwrapped exception.</returns>
        </member>
        <member name="T:Guard">
            <summary>
            Guard class, used for guard clauses and argument validation
            </summary>
        </member>
        <member name="M:Guard.ArgumentNotNull(System.String,System.Object)">
            <summary/>
        </member>
        <member name="M:Guard.ArgumentNotNullOrEmpty(System.String,System.Collections.IEnumerable)">
            <summary/>
        </member>
        <member name="M:Guard.ArgumentValid(System.String,System.Boolean,System.String)">
            <summary/>
        </member>
        <member name="M:Guard.ArgumentValid(System.String,System.Boolean,System.String,System.Object[])">
            <summary/>
        </member>
        <member name="T:NewReflectionExtensions">
            <summary>
            Methods which help bridge and contain the differences between Type and TypeInfo.
            </summary>
        </member>
    </members>
</doc>
