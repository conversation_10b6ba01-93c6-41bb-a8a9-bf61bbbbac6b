<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">

<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

	<appender name="console" class="org.apache.log4j.ConsoleAppender">
		<param name="Target" value="System.out"/>
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="[%d{yyyy-MM-dd HH:mm:ss,SSS}] %-5p %c: %m%n"/>
		</layout>
	</appender>

	<appender name="rollingfiles" class="org.apache.log4j.rolling.RollingFileAppender">
		<rollingPolicy class="org.apache.log4j.rolling.FixedWindowRollingPolicy">
			<param name="fileNamePattern" value="fglib_%i.log"/>
			<param name="minIndex" value="0"/>
			<param name="maxIndex" value="9"/>
		</rollingPolicy>
		<triggeringPolicy class="org.apache.log4j.SizeBasedTriggeringPolicy">
			<param name="MaxFileSize" value="1MB"/>
		</triggeringPolicy>
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="[%d{yyyy-MM-dd HH:mm:ss,SSS}] %-5p %c: %m%n"/>
		</layout>
	</appender>

	<logger name="basler">
		<priority value="info"/>
		<appender-ref ref="console"/>
        <!-- uncomment this if you want to log into files
		<appender-ref ref="rollingfiles"/>
        -->
	</logger>

	<root>
		<priority value="off"/>
	</root>

</log4j:configuration>
