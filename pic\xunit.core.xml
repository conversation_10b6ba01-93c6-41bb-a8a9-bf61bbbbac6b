<?xml version="1.0"?>
<doc>
    <assembly>
        <name>xunit.core</name>
    </assembly>
    <members>
        <member name="T:Xunit.AssemblyTraitAttribute">
            <summary>
            Attribute used to decorate an assembly with arbitrary name/value pairs ("traits").
            </summary>
        </member>
        <member name="M:Xunit.AssemblyTraitAttribute.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.AssemblyTraitAttribute"/> class.
            </summary>
            <param name="name">The trait name</param>
            <param name="value">The trait value</param>
        </member>
        <member name="T:Xunit.ClassDataAttribute">
            <summary>
            Provides a data source for a data theory, with the data coming from a class
            which must implement IEnumerable&lt;object[]&gt;.
            Caution: the property is completely enumerated by .ToList() before any test is run. Hence it should return independent object sets.
            </summary>
        </member>
        <member name="M:Xunit.ClassDataAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.ClassDataAttribute"/> class.
            </summary>
            <param name="class">The class that provides the data.</param>
        </member>
        <member name="P:Xunit.ClassDataAttribute.Class">
            <summary>
            Gets the type of the class that provides the data.
            </summary>
        </member>
        <member name="M:Xunit.ClassDataAttribute.GetData(System.Reflection.MethodInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.CollectionAttribute">
            <summary>
            Used to declare a specific test collection for a test class.
            </summary>
        </member>
        <member name="M:Xunit.CollectionAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.CollectionAttribute" /> class.
            </summary>
            <param name="name">The test collection name.</param>
        </member>
        <member name="T:Xunit.CollectionBehavior">
            <summary>
            Defines the built-in behavior types for collections in xUnit.net.
            </summary>
        </member>
        <member name="F:Xunit.CollectionBehavior.CollectionPerAssembly">
            <summary>
            By default, generates a collection per assembly, and any test classes that are not
            decorated with <see cref="T:Xunit.CollectionAttribute"/> will be placed into the assembly-level
            collection.
            </summary>
        </member>
        <member name="F:Xunit.CollectionBehavior.CollectionPerClass">
            <summary>
            By default, generates a collection per test class for any test classes that are not
            decorated with <see cref="T:Xunit.CollectionAttribute"/>.
            </summary>
        </member>
        <member name="T:Xunit.CollectionBehaviorAttribute">
            <summary>
            Used to declare the default test collection behavior for the assembly.
            </summary>
        </member>
        <member name="M:Xunit.CollectionBehaviorAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.CollectionBehaviorAttribute" /> class.
            </summary>
        </member>
        <member name="M:Xunit.CollectionBehaviorAttribute.#ctor(Xunit.CollectionBehavior)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.CollectionBehaviorAttribute" /> class.
            </summary>
            <param name="collectionBehavior">The collection behavior for the assembly.</param>
        </member>
        <member name="M:Xunit.CollectionBehaviorAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.CollectionBehaviorAttribute" /> class.
            </summary>
            <param name="factoryTypeName">The type name of the test collection factory (that implements <see cref="T:Xunit.Sdk.IXunitTestCollectionFactory"/>).</param>
            <param name="factoryAssemblyName">The assembly that <paramref name="factoryTypeName"/> exists in.</param>
        </member>
        <member name="P:Xunit.CollectionBehaviorAttribute.DisableTestParallelization">
            <summary>
            Determines whether tests in this assembly are run in parallel.
            </summary>
        </member>
        <member name="P:Xunit.CollectionBehaviorAttribute.MaxParallelThreads">
            <summary>
            Determines how many tests can run in parallel with each other. If set to 0, the system will
            use <see cref="P:System.Environment.ProcessorCount"/>. If set to a negative number, then there will
            be no limit to the number of threads.
            </summary>
        </member>
        <member name="T:Xunit.CollectionDefinitionAttribute">
            <summary>
            Used to declare a test collection container class. The container class gives
            developers a place to attach interfaces like <see cref="T:Xunit.IClassFixture`1"/> and
            <see cref="T:Xunit.ICollectionFixture`1"/> that will be applied to all tests classes
            that are members of the test collection.
            </summary>
        </member>
        <member name="M:Xunit.CollectionDefinitionAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.CollectionDefinitionAttribute" /> class.
            </summary>
            <param name="name">The test collection name.</param>
        </member>
        <member name="P:Xunit.CollectionDefinitionAttribute.DisableParallelization">
            <summary>
            Determines whether tests in this collection runs in parallel with any other collections.
            </summary>
        </member>
        <member name="T:Xunit.Extensions.PropertyDataAttribute">
            <summary/>
        </member>
        <member name="M:Xunit.Extensions.PropertyDataAttribute.#ctor(System.String)">
            <summary/>
        </member>
        <member name="P:Xunit.Extensions.PropertyDataAttribute.PropertyType">
            <summary/>
        </member>
        <member name="T:Xunit.FactAttribute">
            <summary>
            Attribute that is applied to a method to indicate that it is a fact that should be run
            by the test runner. It can also be extended to support a customized definition of a
            test method.
            </summary>
        </member>
        <member name="P:Xunit.FactAttribute.DisplayName">
            <summary>
            Gets the name of the test to be used when the test is skipped. Defaults to
            null, which will cause the fully qualified test name to be used.
            </summary>
        </member>
        <member name="P:Xunit.FactAttribute.Skip">
            <summary>
            Marks the test so that it will not be run, and gets or sets the skip reason
            </summary>
        </member>
        <member name="P:Xunit.FactAttribute.Timeout">
            <summary>
            Marks the test as having a timeout, and gets or sets the timeout (in milliseconds).
            WARNING: Using this with parallelization turned on will result in undefined behavior.
            Timeout is only supported when parallelization is disabled, either globally or with
            a parallelization-disabled test collection.
            </summary>
        </member>
        <member name="T:Xunit.IAsyncLifetime">
            <summary>
            Used to provide asynchronous lifetime functionality. Currently supported:
            - Test classes
            - Classes used in <see cref="T:Xunit.IClassFixture`1"/>
            - Classes used in <see cref="T:Xunit.ICollectionFixture`1"/>.
            </summary>
        </member>
        <member name="M:Xunit.IAsyncLifetime.InitializeAsync">
            <summary>
            Called immediately after the class has been created, before it is used.
            </summary>
        </member>
        <member name="M:Xunit.IAsyncLifetime.DisposeAsync">
            <summary>
            Called when an object is no longer needed. Called just before <see cref="M:System.IDisposable.Dispose"/>
            if the class also implements that.
            </summary>
        </member>
        <member name="T:Xunit.IClassFixture`1">
            <summary>
            Used to decorate xUnit.net test classes and collections to indicate a test which has
            per-test-class fixture data. An instance of the fixture data is initialized just before
            the first test in the class is run, and if it implements IDisposable, is disposed
            after the last test in the class is run. To gain access to the fixture data from
            inside the test, a constructor argument should be added to the test class which
            exactly matches the <typeparamref name="TFixture"/>. Class fixtures must have a
            single parameterless constructor, and may take collection fixture types as constructor
            arguments.
            </summary>
            <typeparam name="TFixture">The type of the fixture.</typeparam>
            <remarks>
            If asynchronous setup of <typeparamref name="TFixture"/> is required 
            it should implement the <see cref="T:Xunit.IAsyncLifetime"/> interface.
            </remarks>
        </member>
        <member name="T:Xunit.ICollectionFixture`1">
            <summary>
            Used to decorate xUnit.net test classes and collections to indicate a test which has
            per-test-collection fixture data. An instance of the fixture data is initialized just before
            the first test in the collection is run, and if it implements IDisposable, is disposed
            after the last test in the collection is run. To gain access to the fixture data from
            inside the test, a constructor argument should be added to the test class which
            exactly matches the <typeparamref name="TFixture"/>.
            </summary>
            <typeparam name="TFixture">The type of the fixture.</typeparam>
            <remarks>
            If asynchronous setup of <typeparamref name="TFixture"/> is required 
            it should implement the <see cref="T:Xunit.IAsyncLifetime"/> interface.
            </remarks>
        </member>
        <member name="T:Xunit.InlineDataAttribute">
            <summary>
            Provides a data source for a data theory, with the data coming from inline values.
            </summary>
        </member>
        <member name="M:Xunit.InlineDataAttribute.#ctor(System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.InlineDataAttribute"/> class.
            </summary>
            <param name="data">The data values to pass to the theory.</param>
        </member>
        <member name="M:Xunit.InlineDataAttribute.GetData(System.Reflection.MethodInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.ITestCollectionOrderer">
            <summary>
            A class implements this interface to participate in ordering tests
            for the test runner. Test collection orderers are applied using the
            <see cref="T:Xunit.TestCollectionOrdererAttribute"/>, which can be applied at
            the assembly level.
            </summary>
        </member>
        <member name="M:Xunit.ITestCollectionOrderer.OrderTestCollections(System.Collections.Generic.IEnumerable{Xunit.Abstractions.ITestCollection})">
            <summary>
            Orders test collections for execution.
            </summary>
            <param name="testCollections">The test collections to be ordered.</param>
            <returns>The test collections in the order to be run.</returns>
        </member>
        <member name="T:Xunit.MemberDataAttribute">
            <summary>
            Provides a data source for a data theory, with the data coming from one of the following sources:
            1. A static property
            2. A static field
            3. A static method (with parameters)
            The member must return something compatible with IEnumerable&lt;object[]&gt; with the test data.
            Caution: the property is completely enumerated by .ToList() before any test is run. Hence it should return independent object sets.
            </summary>
        </member>
        <member name="M:Xunit.MemberDataAttribute.#ctor(System.String,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.MemberDataAttribute"/> class.
            </summary>
            <param name="memberName">The name of the public static member on the test class that will provide the test data</param>
            <param name="parameters">The parameters for the member (only supported for methods; ignored for everything else)</param>
        </member>
        <member name="M:Xunit.MemberDataAttribute.ConvertDataItem(System.Reflection.MethodInfo,System.Object)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.MemberDataAttributeBase">
            <summary>
            Provides a base class for attributes that will provide member data. The member data must return
            something compatible with <see cref="T:System.Collections.IEnumerable"/>.
            Caution: the property is completely enumerated by .ToList() before any test is run. Hence it should return independent object sets.
            </summary>
        </member>
        <member name="M:Xunit.MemberDataAttributeBase.#ctor(System.String,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.MemberDataAttributeBase"/> class.
            </summary>
            <param name="memberName">The name of the public static member on the test class that will provide the test data</param>
            <param name="parameters">The parameters for the member (only supported for methods; ignored for everything else)</param>
        </member>
        <member name="P:Xunit.MemberDataAttributeBase.DisableDiscoveryEnumeration">
            <summary>
            Returns <c>true</c> if the data attribute wants to skip enumerating data during discovery.
            This will cause the theory to yield a single test case for all data, and the data discovery
            will be during test execution instead of discovery.
            </summary>
        </member>
        <member name="P:Xunit.MemberDataAttributeBase.MemberName">
            <summary>
            Gets the member name.
            </summary>
        </member>
        <member name="P:Xunit.MemberDataAttributeBase.MemberType">
            <summary>
            Gets or sets the type to retrieve the member from. If not set, then the property will be
            retrieved from the unit test class.
            </summary>
        </member>
        <member name="P:Xunit.MemberDataAttributeBase.Parameters">
            <summary>
            Gets or sets the parameters passed to the member. Only supported for static methods.
            </summary>
        </member>
        <member name="M:Xunit.MemberDataAttributeBase.GetData(System.Reflection.MethodInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.MemberDataAttributeBase.ConvertDataItem(System.Reflection.MethodInfo,System.Object)">
            <summary>
            Converts an item yielded by the data member to an object array, for return from <see cref="M:Xunit.MemberDataAttributeBase.GetData(System.Reflection.MethodInfo)"/>.
            </summary>
            <param name="testMethod">The method that is being tested.</param>
            <param name="item">An item yielded from the data member.</param>
            <returns>An <see cref="T:object[]"/> suitable for return from <see cref="M:Xunit.MemberDataAttributeBase.GetData(System.Reflection.MethodInfo)"/>.</returns>
        </member>
        <member name="T:Xunit.Record">
            <summary>
            Allows the user to record actions for a test.
            </summary>
        </member>
        <member name="M:Xunit.Record.Exception(System.Action)">
            <summary>
            Records any exception which is thrown by the given code.
            </summary>
            <param name="testCode">The code which may throw an exception.</param>
            <returns>Returns the exception that was thrown by the code; null, otherwise.</returns>
        </member>
        <member name="M:Xunit.Record.Exception(System.Func{System.Object})">
            <summary>
            Records any exception which is thrown by the given code that has
            a return value. Generally used for testing property accessors.
            </summary>
            <param name="testCode">The code which may throw an exception.</param>
            <returns>Returns the exception that was thrown by the code; null, otherwise.</returns>
        </member>
        <member name="M:Xunit.Record.Exception(System.Func{System.Threading.Tasks.Task})">
            <summary/>
        </member>
        <member name="M:Xunit.Record.ExceptionAsync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Records any exception which is thrown by the given task.
            </summary>
            <param name="testCode">The task which may throw an exception.</param>
            <returns>Returns the exception that was thrown by the code; null, otherwise.</returns>
        </member>
        <member name="M:Xunit.Record.GuardArgumentNotNull(System.String,System.Object)">
            <summary/>
        </member>
        <member name="T:Xunit.Sdk.AssemblyTraitDiscoverer">
            <summary>
            The implementation of <see cref="T:Xunit.Sdk.ITraitDiscoverer"/> which returns the trait values
            for <see cref="T:Xunit.AssemblyTraitAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.AssemblyTraitDiscoverer.GetTraits(Xunit.Abstractions.IAttributeInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.BeforeAfterTestAttribute">
            <summary>
            Base attribute which indicates a test method interception (allows code to be run before and
            after the test is run).
            </summary>
        </member>
        <member name="M:Xunit.Sdk.BeforeAfterTestAttribute.After(System.Reflection.MethodInfo)">
            <summary>
            This method is called after the test method is executed.
            </summary>
            <param name="methodUnderTest">The method under test</param>
        </member>
        <member name="M:Xunit.Sdk.BeforeAfterTestAttribute.Before(System.Reflection.MethodInfo)">
            <summary>
            This method is called before the test method is executed.
            </summary>
            <param name="methodUnderTest">The method under test</param>
        </member>
        <member name="T:Xunit.Sdk.DataAttribute">
            <summary>
            Abstract attribute which represents a data source for a data theory.
            Data source providers derive from this attribute and implement GetData
            to return the data for the theory.
            Caution: the property is completely enumerated by .ToList() before any test is run. Hence it should return independent object sets.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DataAttribute.GetData(System.Reflection.MethodInfo)">
            <summary>
            Returns the data to be used to test the theory.
            </summary>
            <param name="testMethod">The method that is being tested</param>
            <returns>One or more sets of theory data. Each invocation of the test method
            is represented by a single object array.</returns>
        </member>
        <member name="P:Xunit.Sdk.DataAttribute.Skip">
            <summary>
            Marks all test cases generated by this data source as skipped.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.DataDiscoverer">
            <summary>
            Default implementation of <see cref="T:Xunit.Sdk.IDataDiscoverer"/>. Uses reflection to find the
            data associated with <see cref="T:Xunit.Sdk.DataAttribute"/>; may return <c>null</c> when called
            without reflection-based abstraction implementations.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DataDiscoverer.GetData(Xunit.Abstractions.IAttributeInfo,Xunit.Abstractions.IMethodInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.DataDiscoverer.SupportsDiscoveryEnumeration(Xunit.Abstractions.IAttributeInfo,Xunit.Abstractions.IMethodInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.DataDiscovererAttribute">
            <summary>
            An attribute used to decorate classes which derive from <see cref="T:Xunit.Sdk.DataAttribute"/>,
            to indicate how data elements should be discovered.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.DataDiscovererAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Xunit.Sdk.DataDiscovererAttribute"/>.
            </summary>
            <param name="typeName">The fully qualified type name of the discoverer
            (f.e., 'Xunit.Sdk.DataDiscoverer')</param>
            <param name="assemblyName">The name of the assembly that the discoverer type
            is located in, without file extension (f.e., 'xunit.execution')</param>
        </member>
        <member name="T:Xunit.Sdk.ExceptionAggregator">
            <summary>
            Aggregates exceptions. Intended to run one or more code blocks, and collect the
            exceptions thrown by those code blocks.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExceptionAggregator.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ExceptionAggregator"/> class.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExceptionAggregator.#ctor(Xunit.Sdk.ExceptionAggregator)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.Sdk.ExceptionAggregator"/> class that
            contains the exception list of its parent.
            </summary>
            <param name="parent">The parent aggregator to copy exceptions from.</param>
        </member>
        <member name="P:Xunit.Sdk.ExceptionAggregator.HasExceptions">
            <summary>
            Returns <c>true</c> if the aggregator has at least one exception inside it.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExceptionAggregator.Add(System.Exception)">
            <summary>
            Adds an exception to the aggregator.
            </summary>
            <param name="ex">The exception to be added.</param>
        </member>
        <member name="M:Xunit.Sdk.ExceptionAggregator.Aggregate(Xunit.Sdk.ExceptionAggregator)">
            <summary>
            Adds exceptions from another aggregator into this aggregator.
            </summary>
            <param name="aggregator">The aggregator whose exceptions should be copied.</param>
        </member>
        <member name="M:Xunit.Sdk.ExceptionAggregator.Clear">
            <summary>
            Clears the aggregator.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ExceptionAggregator.Run(System.Action)">
            <summary>
            Runs the code, catching the exception that is thrown and adding it to
            the aggregate.
            </summary>
            <param name="code">The code to be run.</param>
        </member>
        <member name="M:Xunit.Sdk.ExceptionAggregator.RunAsync(System.Func{System.Threading.Tasks.Task})">
            <summary>
            Runs the code, catching the exception that is thrown and adding it to
            the aggregate.
            </summary>
            <param name="code">The code to be run.</param>
        </member>
        <member name="M:Xunit.Sdk.ExceptionAggregator.RunAsync``1(System.Func{System.Threading.Tasks.Task{``0}})">
            <summary>
            Runs the code, catching the exception that is thrown and adding it to
            the aggregate.
            </summary>
            <param name="code">The code to be run.</param>
        </member>
        <member name="M:Xunit.Sdk.ExceptionAggregator.ToException">
            <summary>
            Returns an exception that represents the exceptions thrown by the code
            passed to the <see cref="M:Xunit.Sdk.ExceptionAggregator.Run(System.Action)"/> or <see cref="M:Xunit.Sdk.ExceptionAggregator.RunAsync(System.Func{System.Threading.Tasks.Task})"/> method.
            </summary>
            <returns>Returns <c>null</c> if no exceptions were thrown; returns the
            exact exception if a single exception was thrown; returns <see cref="T:System.AggregateException"/>
            if more than one exception was thrown.</returns>
        </member>
        <member name="T:Xunit.Sdk.IDataDiscoverer">
            <summary>
            This class is responsible for discovering the data available in an implementation
            of <see cref="T:Xunit.Sdk.DataAttribute"/>. The discovery process may not always have access
            to reflection (i.e., running in Resharper), so the discoverer must make a best
            effort to return data, but may return null when there is not enough information
            available (for example, if reflection is required to answer the question).
            </summary>
        </member>
        <member name="M:Xunit.Sdk.IDataDiscoverer.GetData(Xunit.Abstractions.IAttributeInfo,Xunit.Abstractions.IMethodInfo)">
            <summary>
            Returns the data to be used to test the theory.
            </summary>
            <remarks>
            This will be called during
            discovery, at which point the <paramref name="testMethod"/> may or may not
            be backed by reflection (i.e., implementing <see cref="T:Xunit.Abstractions.IReflectionMethodInfo"/>).
            If the data is not available because reflection is required, then you may return
            null to inform xUnit that the quantity of data is unknown at this point.
            When the tests are run, if you returned back null during discovery, then this method
            will be called again to retrieve the data, this time guaranteed to provide
            an implementation of <see cref="T:Xunit.Abstractions.IReflectionMethodInfo"/>. At this time, you
            must return the actual data, and returning null is not legal.
            </remarks>
            <param name="dataAttribute">The data attribute being discovered</param>
            <param name="testMethod">The method that is being tested/discovered</param>
            <returns>The theory data (or null during discovery, if not enough
            information is available to enumerate the data)</returns>
        </member>
        <member name="M:Xunit.Sdk.IDataDiscoverer.SupportsDiscoveryEnumeration(Xunit.Abstractions.IAttributeInfo,Xunit.Abstractions.IMethodInfo)">
            <summary>
            Returns <c>true</c> if the data attribute supports enumeration during
            discovery; <c>false</c> otherwise. Data attributes with expensive computational
            costs and/or randomized data sets should return <c>false</c>.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.IMessageBus">
            <summary>
            Used by discovery, execution, and extensibility code to send messages to the runner.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.IMessageBus.QueueMessage(Xunit.Abstractions.IMessageSinkMessage)">
            <summary>
            Queues a message to be sent to the runner.
            </summary>
            <param name="message">The message to be sent to the runner</param>
            <returns>
            Returns <c>true</c> if discovery/execution should continue; <c>false</c>, otherwise.
            The return value may be safely ignored by components which are not directly responsible
            for discovery or execution, and this is intended to communicate to those sub-systems that
            that they should short circuit and stop their work as quickly as is reasonable.
            </returns>
        </member>
        <member name="T:Xunit.Sdk.InlineDataDiscoverer">
            <summary>
            Implementation of <see cref="T:Xunit.Sdk.IDataDiscoverer"/> used to discover the data
            provided by <see cref="T:Xunit.InlineDataAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.InlineDataDiscoverer.GetData(Xunit.Abstractions.IAttributeInfo,Xunit.Abstractions.IMethodInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.Sdk.InlineDataDiscoverer.SupportsDiscoveryEnumeration(Xunit.Abstractions.IAttributeInfo,Xunit.Abstractions.IMethodInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.ITestCaseOrderer">
            <summary>
            A class implements this interface to participate in ordering tests
            for the test runner. Test case orderers are applied using the
            <see cref="T:Xunit.TestCaseOrdererAttribute"/>, which can be applied at
            the assembly, test collection, and test class level.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ITestCaseOrderer.OrderTestCases``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Orders test cases for execution.
            </summary>
            <param name="testCases">The test cases to be ordered.</param>
            <returns>The test cases in the order to be run.</returns>
        </member>
        <member name="T:Xunit.Sdk.ITestFrameworkAttribute">
            <summary>
            Marker interface that must be implemented by test framework attributes, so
            that the test framework attribute discoverer can find them.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.ITestFrameworkTypeDiscoverer">
            <summary>
            Interface to be implemented by classes which are used to discover the test framework.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ITestFrameworkTypeDiscoverer.GetTestFrameworkType(Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Gets the type that implements <see cref="T:Xunit.Abstractions.ITestFramework"/> to be used to discover
            and run tests.
            </summary>
            <param name="attribute">The test framework attribute that decorated the assembly</param>
            <returns>The test framework type</returns>
        </member>
        <member name="T:Xunit.Sdk.ITraitAttribute">
            <summary>
            Marker interface used by attributes which provide trait data.
            </summary>
        </member>
        <member name="T:Xunit.Sdk.ITraitDiscoverer">
            <summary>
            This interface is implemented by discoverers that provide trait values to
            xUnit.net v2 tests.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.ITraitDiscoverer.GetTraits(Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Gets the trait values from the trait attribute.
            </summary>
            <param name="traitAttribute">The trait attribute containing the trait values.</param>
            <returns>The trait values.</returns>
        </member>
        <member name="T:Xunit.Sdk.IXunitTestCase">
            <summary>
            Represents a single test case from xUnit.net v2.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.IXunitTestCase.InitializationException">
            <summary>
            Gets the exception that happened during initialization. When this is set, then
            the test execution should fail with this exception.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.IXunitTestCase.Method">
            <summary>
            Gets the method to be run. Differs from <see cref="T:Xunit.Abstractions.ITestCase"/>.<see cref="P:Xunit.Abstractions.ITestMethod.Method"/> in that
            any generic argument types will have been closed based on the arguments.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.IXunitTestCase.Timeout">
            <summary>
            Gets the timeout of the test, in milliseconds; if zero or negative, means the test case has no timeout.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.IXunitTestCase.RunAsync(Xunit.Abstractions.IMessageSink,Xunit.Sdk.IMessageBus,System.Object[],Xunit.Sdk.ExceptionAggregator,System.Threading.CancellationTokenSource)">
            <summary>
            Executes the test case, returning 0 or more result messages through the message sink.
            </summary>
            <param name="diagnosticMessageSink">The message sink used to send diagnostic messages to.</param>
            <param name="messageBus">The message bus to report results to.</param>
            <param name="constructorArguments">The arguments to pass to the constructor.</param>
            <param name="aggregator">The error aggregator to use for catching exception.</param>
            <param name="cancellationTokenSource">The cancellation token source that indicates whether cancellation has been requested.</param>
            <returns>Returns the summary of the test case run.</returns>
        </member>
        <member name="T:Xunit.Sdk.IXunitTestCaseDiscoverer">
            <summary>
            Interface to be implemented by classes which are used to discover tests cases attached
            to test methods that are attributed with <see cref="T:Xunit.FactAttribute"/> (or a subclass).
            </summary>
        </member>
        <member name="M:Xunit.Sdk.IXunitTestCaseDiscoverer.Discover(Xunit.Abstractions.ITestFrameworkDiscoveryOptions,Xunit.Abstractions.ITestMethod,Xunit.Abstractions.IAttributeInfo)">
            <summary>
            Discover test cases from a test method.
            </summary>
            <param name="discoveryOptions">The discovery options to be used.</param>
            <param name="testMethod">The test method the test cases belong to.</param>
            <param name="factAttribute">The fact attribute attached to the test method.</param>
            <returns>Returns zero or more test cases represented by the test method.</returns>
        </member>
        <member name="T:Xunit.Sdk.IXunitTestCollectionFactory">
            <summary>
            This interface is intended to be implemented by components which generate test collections.
            End users specify the desired test collection factory by applying <see cref="T:Xunit.CollectionBehaviorAttribute"/>
            at the assembly level. Classes which implement this interface must have a constructor
            that takes <see cref="T:Xunit.Abstractions.ITestAssembly"/> and <see cref="T:Xunit.Abstractions.IMessageSink"/>.
            </summary>
        </member>
        <member name="P:Xunit.Sdk.IXunitTestCollectionFactory.DisplayName">
            <summary>
            Gets the display name for the test collection factory. This information is shown to the end
            user as part of the description of the test environment.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.IXunitTestCollectionFactory.Get(Xunit.Abstractions.ITypeInfo)">
            <summary>
            Gets the test collection for a given test class.
            </summary>
            <param name="testClass">The test class.</param>
            <returns>The test collection.</returns>
        </member>
        <member name="T:Xunit.Sdk.MemberDataDiscoverer">
            <summary>
            Implementation of <see cref="T:Xunit.Sdk.IDataDiscoverer"/> for discovering <see cref="T:Xunit.MemberDataAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.MemberDataDiscoverer.SupportsDiscoveryEnumeration(Xunit.Abstractions.IAttributeInfo,Xunit.Abstractions.IMethodInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.PlatformSpecificAssemblyAttribute">
             <summary>
             Marks an assembly as a platform specific assembly for use with xUnit.net. Type references from
             such assemblies are allowed to use a special suffix ("My.Assembly.{Platform}"), which will
             automatically be translated into the correct platform-specific name ("My.Assembly.desktop",
             "My.Assembly.win8", etc.). This affects both extensibility points which require specifying
             a string-based type name and assembly, as well as serialization.
            
             In v2.1 and later, the supported platform target names include:
            
               "desktop" (for desktop and PCL tests),
               "dotnet" (everything else).
            
             In v2.0, the following names were also supported:
             
               "iOS-Universal" (for Xamarin test projects targeting iOS),
               "MonoAndroid" (for Xamarin MonoAndroid tests),
               "MonoTouch" (for Xamarin MonoTouch tests),
               "universal" (for Windows Phone 8.1 and Windows 8.1 tests),
               "win8" (for Windows 8 tests),
               "wp8" (for Windows Phone 8 Silverlight tests).
            
             For backward compatibility reasons, the v2.1 runners will support tests linked against
             the v2.0 execution libraries.
            
             Note that file names may be case sensitive (when running on platforms with case sensitive
             file systems like Linux), so ensure that your assembly file name casing is consistent, and
             that you use the suffixes here with the exact case shown.
             </summary>
        </member>
        <member name="T:Xunit.Sdk.RunSummary">
            <summary>
            Represents the statistical summary from a run of one or more tests.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.RunSummary.Total">
            <summary>
            The total number of tests run.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.RunSummary.Failed">
            <summary>
            The number of failed tests.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.RunSummary.Skipped">
            <summary>
            The number of skipped tests.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.RunSummary.Time">
            <summary>
            The total time taken to run the tests, in seconds.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.RunSummary.Aggregate(Xunit.Sdk.RunSummary)">
            <summary>
            Adds a run summary's totals into this run summary.
            </summary>
            <param name="other">The run summary to be added.</param>
        </member>
        <member name="T:Xunit.Sdk.TestFrameworkDiscovererAttribute">
            <summary>
            Decorates an implementation of <see cref="T:Xunit.Abstractions.ITestFrameworkDiscoverer"/> that is used to
            determine which test framework is used to discover and run tests.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TestFrameworkDiscovererAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Xunit.Sdk.TestFrameworkDiscovererAttribute"/>.
            </summary>
            <param name="typeName">The fully qualified type name of the discoverer
            (f.e., 'Xunit.Sdk.DataDiscoverer')</param>
            <param name="assemblyName">The name of the assembly that the discoverer type
            is located in, without file extension (f.e., 'xunit.execution')</param>
        </member>
        <member name="T:Xunit.Sdk.TraitDiscoverer">
            <summary>
            The implementation of <see cref="T:Xunit.Sdk.ITraitDiscoverer"/> which returns the trait values
            for <see cref="T:Xunit.TraitAttribute"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TraitDiscoverer.GetTraits(Xunit.Abstractions.IAttributeInfo)">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.Sdk.TraitDiscovererAttribute">
            <summary>
            An attribute used to decorate classes which implement <see cref="T:Xunit.Sdk.ITraitAttribute"/>,
            to indicate how trait values should be discovered. The discoverer type must implement
            <see cref="T:Xunit.Sdk.ITraitDiscoverer"/>.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.TraitDiscovererAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Xunit.Sdk.TraitDiscovererAttribute"/>.
            </summary>
            <param name="typeName">The fully qualified type name of the discoverer
            (f.e., 'Xunit.Sdk.TraitDiscoverer')</param>
            <param name="assemblyName">The name of the assembly that the discoverer type
            is located in, without file extension (f.e., 'xunit.execution')</param>
        </member>
        <member name="T:Xunit.Sdk.XunitTestCaseDiscovererAttribute">
            <summary>
            An attribute used to decorate classes which derive from <see cref="T:Xunit.FactAttribute"/>,
            to indicate how test cases should be discovered.
            </summary>
        </member>
        <member name="M:Xunit.Sdk.XunitTestCaseDiscovererAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes an instance of the <see cref="T:Xunit.Sdk.XunitTestCaseDiscovererAttribute"/> class.
            </summary>
            <param name="typeName">The fully qualified type name of the discoverer
            (f.e., 'Xunit.Sdk.FactDiscoverer')</param>
            <param name="assemblyName">The name of the assembly that the discoverer type
            is located in, without file extension (f.e., 'xunit.execution')</param>
        </member>
        <member name="T:Xunit.Sdk.TestMethodDisplay">
            <summary>
            Indicates the default display name format for test methods.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.TestMethodDisplay.ClassAndMethod">
            <summary>
            Use a fully qualified name (namespace + class + method)
            </summary>
        </member>
        <member name="F:Xunit.Sdk.TestMethodDisplay.Method">
            <summary>
            Use just the method name (without class)
            </summary>
        </member>
        <member name="T:Xunit.Sdk.TestMethodDisplayOptions">
            <summary>
            Indicates the method display options for test methods.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.TestMethodDisplayOptions.None">
            <summary>
            Indicates no additional method display options.
            </summary>
            <remarks>This is the default configuration option.</remarks>
        </member>
        <member name="F:Xunit.Sdk.TestMethodDisplayOptions.ReplaceUnderscoreWithSpace">
            <summary>
            Replace underscores in display names with a space.
            </summary>
        </member>
        <member name="F:Xunit.Sdk.TestMethodDisplayOptions.UseOperatorMonikers">
            <summary>
            Replace well-known monikers with their equivalent operator.
            </summary>
            <list type="bullet">
            <item><description>lt : &lt;</description></item>
            <item><description>le : &lt;=</description></item>
            <item><description>eq : =</description></item>
            <item><description>ne : !=</description></item>
            <item><description>gt : &gt;</description></item>
            <item><description>ge : &gt;=</description></item>
            </list>
        </member>
        <member name="F:Xunit.Sdk.TestMethodDisplayOptions.UseEscapeSequences">
            <summary>
            Replace supported escape sequences with their equivalent character.
            <list type="table">
            <listheader>
             <term>Encoding</term>
             <description>Format</description>
            </listheader>
            <item><term>ASCII</term><description>X hex-digit hex-digit (ex: X2C)</description></item>
            <item><term>Unicode</term><description>U hex-digit hex-digit hex-digit hex-digit (ex: U00A9)</description></item>
            </list>
            </summary>
        </member>
        <member name="F:Xunit.Sdk.TestMethodDisplayOptions.ReplacePeriodWithComma">
            <summary>
            Replaces the period delimiter used in namespace and type references with a comma.
            </summary>
            <remarks>This option is only honored if the <see cref="F:Xunit.Sdk.TestMethodDisplay.ClassAndMethod"/> setting is also enabled.</remarks>
        </member>
        <member name="F:Xunit.Sdk.TestMethodDisplayOptions.All">
            <summary>
            Enables all method display options.
            </summary>
        </member>
        <member name="T:Xunit.TestCaseOrdererAttribute">
            <summary>
            Used to decorate an assembly, test collection, or test class to allow
            the use of a custom <see cref="T:Xunit.Sdk.ITestCaseOrderer"/>.
            </summary>
        </member>
        <member name="M:Xunit.TestCaseOrdererAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.TestCaseOrdererAttribute"/> class.
            </summary>
            <param name="ordererTypeName">The type name of the orderer class (that implements <see cref="T:Xunit.Sdk.ITestCaseOrderer"/>).</param>
            <param name="ordererAssemblyName">The assembly that <paramref name="ordererTypeName"/> exists in.</param>
        </member>
        <member name="T:Xunit.TestCollectionOrdererAttribute">
            <summary>
            Used to decorate an assembly to allow the use of a custom <see cref="T:Xunit.Sdk.ITestCollectionOrderer"/>.
            </summary>
        </member>
        <member name="M:Xunit.TestCollectionOrdererAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Xunit.TestCollectionOrdererAttribute"/> class.
            </summary>
            <param name="ordererTypeName">The type name of the orderer class (that implements <see cref="T:Xunit.Sdk.ITestCollectionOrderer"/>).</param>
            <param name="ordererAssemblyName">The assembly that <paramref name="ordererTypeName"/> exists in.</param>
        </member>
        <member name="T:Xunit.TestFrameworkAttribute">
            <summary>
            Used to decorate an assembly to allow the use of a custom <see cref="T:Xunit.Sdk.ITestFramework"/>.
            </summary>
        </member>
        <member name="M:Xunit.TestFrameworkAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes an instance of <see cref="T:Xunit.TestFrameworkAttribute"/>.
            </summary>
            <param name="typeName">The fully qualified type name of the test framework
            (f.e., 'Xunit.Sdk.XunitTestFramework')</param>
            <param name="assemblyName">The name of the assembly that the test framework type
            is located in, without file extension (f.e., 'xunit.execution')</param>
        </member>
        <member name="T:Xunit.TheoryAttribute">
            <summary>
            Marks a test method as being a data theory. Data theories are tests which are fed
            various bits of data from a data source, mapping to parameters on the test method.
            If the data source contains multiple rows, then the test method is executed
            multiple times (once with each data row). Data is provided by attributes which
            derive from <see cref="T:Xunit.Sdk.DataAttribute"/> (notably, <see cref="T:Xunit.InlineDataAttribute"/> and
            <see cref="T:Xunit.MemberDataAttribute"/>).
            </summary>
        </member>
        <member name="T:Xunit.TheoryData">
            <summary>
            Provides data for theories based on collection initialization syntax.
            </summary>
        </member>
        <member name="M:Xunit.TheoryData.AddRow(System.Object[])">
            <summary>
            Adds a row to the theory.
            </summary>
            <param name="values">The values to be added.</param>
        </member>
        <member name="M:Xunit.TheoryData.GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="M:Xunit.TheoryData.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc/>
        </member>
        <member name="T:Xunit.TheoryData`1">
            <summary>
            Represents a set of data for a theory with a single parameter. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T">The parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`1.Add(`0)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p">The data value.</param>
        </member>
        <member name="T:Xunit.TheoryData`2">
            <summary>
            Represents a set of data for a theory with 2 parameters. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T1">The first parameter type.</typeparam>
            <typeparam name="T2">The second parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`2.Add(`0,`1)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p1">The first data value.</param>
            <param name="p2">The second data value.</param>
        </member>
        <member name="T:Xunit.TheoryData`3">
            <summary>
            Represents a set of data for a theory with 3 parameters. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T1">The first parameter type.</typeparam>
            <typeparam name="T2">The second parameter type.</typeparam>
            <typeparam name="T3">The third parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`3.Add(`0,`1,`2)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p1">The first data value.</param>
            <param name="p2">The second data value.</param>
            <param name="p3">The third data value.</param>
        </member>
        <member name="T:Xunit.TheoryData`4">
            <summary>
            Represents a set of data for a theory with 4 parameters. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T1">The first parameter type.</typeparam>
            <typeparam name="T2">The second parameter type.</typeparam>
            <typeparam name="T3">The third parameter type.</typeparam>
            <typeparam name="T4">The fourth parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`4.Add(`0,`1,`2,`3)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p1">The first data value.</param>
            <param name="p2">The second data value.</param>
            <param name="p3">The third data value.</param>
            <param name="p4">The fourth data value.</param>
        </member>
        <member name="T:Xunit.TheoryData`5">
            <summary>
            Represents a set of data for a theory with 5 parameters. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T1">The first parameter type.</typeparam>
            <typeparam name="T2">The second parameter type.</typeparam>
            <typeparam name="T3">The third parameter type.</typeparam>
            <typeparam name="T4">The fourth parameter type.</typeparam>
            <typeparam name="T5">The fifth parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`5.Add(`0,`1,`2,`3,`4)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p1">The first data value.</param>
            <param name="p2">The second data value.</param>
            <param name="p3">The third data value.</param>
            <param name="p4">The fourth data value.</param>
            <param name="p5">The fifth data value.</param>
        </member>
        <member name="T:Xunit.TheoryData`6">
            <summary>
            Represents a set of data for a theory with 5 parameters. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T1">The first parameter type.</typeparam>
            <typeparam name="T2">The second parameter type.</typeparam>
            <typeparam name="T3">The third parameter type.</typeparam>
            <typeparam name="T4">The fourth parameter type.</typeparam>
            <typeparam name="T5">The fifth parameter type.</typeparam>
            <typeparam name="T6">The sixth parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`6.Add(`0,`1,`2,`3,`4,`5)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p1">The first data value.</param>
            <param name="p2">The second data value.</param>
            <param name="p3">The third data value.</param>
            <param name="p4">The fourth data value.</param>
            <param name="p5">The fifth data value.</param>
            <param name="p6">The sixth data value.</param>
        </member>
        <member name="T:Xunit.TheoryData`7">
            <summary>
            Represents a set of data for a theory with 5 parameters. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T1">The first parameter type.</typeparam>
            <typeparam name="T2">The second parameter type.</typeparam>
            <typeparam name="T3">The third parameter type.</typeparam>
            <typeparam name="T4">The fourth parameter type.</typeparam>
            <typeparam name="T5">The fifth parameter type.</typeparam>
            <typeparam name="T6">The sixth parameter type.</typeparam>
            <typeparam name="T7">The seventh parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`7.Add(`0,`1,`2,`3,`4,`5,`6)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p1">The first data value.</param>
            <param name="p2">The second data value.</param>
            <param name="p3">The third data value.</param>
            <param name="p4">The fourth data value.</param>
            <param name="p5">The fifth data value.</param>
            <param name="p6">The sixth data value.</param>
            <param name="p7">The seventh data value.</param>
        </member>
        <member name="T:Xunit.TheoryData`8">
            <summary>
            Represents a set of data for a theory with 5 parameters. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T1">The first parameter type.</typeparam>
            <typeparam name="T2">The second parameter type.</typeparam>
            <typeparam name="T3">The third parameter type.</typeparam>
            <typeparam name="T4">The fourth parameter type.</typeparam>
            <typeparam name="T5">The fifth parameter type.</typeparam>
            <typeparam name="T6">The sixth parameter type.</typeparam>
            <typeparam name="T7">The seventh parameter type.</typeparam>
            <typeparam name="T8">The eigth parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`8.Add(`0,`1,`2,`3,`4,`5,`6,`7)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p1">The first data value.</param>
            <param name="p2">The second data value.</param>
            <param name="p3">The third data value.</param>
            <param name="p4">The fourth data value.</param>
            <param name="p5">The fifth data value.</param>
            <param name="p6">The sixth data value.</param>
            <param name="p7">The seventh data value.</param>
            <param name="p8">The eigth data value.</param>
        </member>
        <member name="T:Xunit.TheoryData`9">
            <summary>
            Represents a set of data for a theory with 5 parameters. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T1">The first parameter type.</typeparam>
            <typeparam name="T2">The second parameter type.</typeparam>
            <typeparam name="T3">The third parameter type.</typeparam>
            <typeparam name="T4">The fourth parameter type.</typeparam>
            <typeparam name="T5">The fifth parameter type.</typeparam>
            <typeparam name="T6">The sixth parameter type.</typeparam>
            <typeparam name="T7">The seventh parameter type.</typeparam>
            <typeparam name="T8">The eigth parameter type.</typeparam>
            <typeparam name="T9">The nineth parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`9.Add(`0,`1,`2,`3,`4,`5,`6,`7,`8)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p1">The first data value.</param>
            <param name="p2">The second data value.</param>
            <param name="p3">The third data value.</param>
            <param name="p4">The fourth data value.</param>
            <param name="p5">The fifth data value.</param>
            <param name="p6">The sixth data value.</param>
            <param name="p7">The seventh data value.</param>
            <param name="p8">The eigth data value.</param>
            <param name="p9">The nineth data value.</param>
        </member>
        <member name="T:Xunit.TheoryData`10">
            <summary>
            Represents a set of data for a theory with 5 parameters. Data can
            be added to the data set using the collection initializer syntax.
            </summary>
            <typeparam name="T1">The first parameter type.</typeparam>
            <typeparam name="T2">The second parameter type.</typeparam>
            <typeparam name="T3">The third parameter type.</typeparam>
            <typeparam name="T4">The fourth parameter type.</typeparam>
            <typeparam name="T5">The fifth parameter type.</typeparam>
            <typeparam name="T6">The sixth parameter type.</typeparam>
            <typeparam name="T7">The seventh parameter type.</typeparam>
            <typeparam name="T8">The eigth parameter type.</typeparam>
            <typeparam name="T9">The nineth parameter type.</typeparam>
            <typeparam name="T10">The tenth parameter type.</typeparam>
        </member>
        <member name="M:Xunit.TheoryData`10.Add(`0,`1,`2,`3,`4,`5,`6,`7,`8,`9)">
            <summary>
            Adds data to the theory data set.
            </summary>
            <param name="p1">The first data value.</param>
            <param name="p2">The second data value.</param>
            <param name="p3">The third data value.</param>
            <param name="p4">The fourth data value.</param>
            <param name="p5">The fifth data value.</param>
            <param name="p6">The sixth data value.</param>
            <param name="p7">The seventh data value.</param>
            <param name="p8">The eigth data value.</param>
            <param name="p9">The nineth data value.</param>
            <param name="p10">The tenth data value.</param>
        </member>
        <member name="T:Xunit.TraitAttribute">
            <summary>
            Attribute used to decorate a test method with arbitrary name/value pairs ("traits").
            </summary>
        </member>
        <member name="M:Xunit.TraitAttribute.#ctor(System.String,System.String)">
            <summary>
            Creates a new instance of the <see cref="T:Xunit.TraitAttribute"/> class.
            </summary>
            <param name="name">The trait name</param>
            <param name="value">The trait value</param>
        </member>
        <member name="T:Xunit.Internal.ArgumentFormatter">
            <summary>
            Formats value for display in assertion messages and data-driven test display names.
            </summary>
        </member>
        <member name="F:Xunit.Internal.ArgumentFormatter.MAX_DEPTH">
            <summary>
            Gets the maximum printing depth, in terms of objects before truncation.
            </summary>
        </member>
        <member name="F:Xunit.Internal.ArgumentFormatter.MAX_ENUMERABLE_LENGTH">
            <summary>
            Gets the maximum number of values printed for collections before truncation.
            </summary>
        </member>
        <member name="F:Xunit.Internal.ArgumentFormatter.MAX_OBJECT_ITEM_COUNT">
            <summary>
            Gets the maximum number of items (properties or fields) printed in an object before truncation.
            </summary>
        </member>
        <member name="F:Xunit.Internal.ArgumentFormatter.MAX_STRING_LENGTH">
            <summary>
            Gets the maximum strength length before truncation.
            </summary>
        </member>
        <member name="P:Xunit.Internal.ArgumentFormatter.Ellipsis">
            <summary>
            Gets the ellipsis value (three middle dots, aka U+00B7).
            </summary>
        </member>
        <member name="M:Xunit.Internal.ArgumentFormatter.EscapeString(System.String)">
            <summary>
            Escapes a string for printing, attempting to most closely model the value on how you would
            enter the value in a C# string literal. That means control codes that are normally backslash
            escaped (like "\n" for newline) are represented like that; all other control codes for ASCII
            values under 32 are printed as "\xnn".
            </summary>
            <param name="s">The string value to be escaped</param>
        </member>
        <member name="M:Xunit.Internal.ArgumentFormatter.Format(System.Object,System.Int32)">
            <summary>
            Formats a value for display.
            </summary>
            <param name="value">The value to be formatted</param>
            <param name="depth">The optional printing depth (1 indicates a top-level value)</param>
        </member>
        <member name="M:Xunit.Internal.ArgumentFormatter.FormatTypeName(System.Type,System.Boolean)">
            <summary>
            Formats a type. This maps built-in C# types to their C# native name (e.g., printing "int" instead
            of "Int32" or "System.Int32").
            </summary>
            <param name="type">The type to get the formatted name of</param>
            <param name="fullTypeName">Set to <c>true</c> to include the namespace; set to <c>false</c> for just the simple type name</param>
        </member>
        <member name="M:ExceptionExtensions.RethrowWithNoStackTraceLoss(System.Exception)">
            <summary>
            Rethrows an exception object without losing the existing stack trace information
            </summary>
            <param name="ex">The exception to re-throw.</param>
            <remarks>
            For more information on this technique, see
            http://www.dotnetjunkies.com/WebLog/chris.taylor/archive/2004/03/03/8353.aspx.
            The remote_stack_trace string is here to support Mono.
            </remarks>
        </member>
        <member name="M:ExceptionExtensions.Unwrap(System.Exception)">
            <summary>
            Unwraps an exception to remove any wrappers, like <see cref="T:System.Reflection.TargetInvocationException"/>.
            </summary>
            <param name="ex">The exception to unwrap.</param>
            <returns>The unwrapped exception.</returns>
        </member>
        <member name="T:Guard">
            <summary>
            Guard class, used for guard clauses and argument validation
            </summary>
        </member>
        <member name="M:Guard.ArgumentNotNull(System.String,System.Object)">
            <summary/>
        </member>
        <member name="M:Guard.ArgumentNotNullOrEmpty(System.String,System.Collections.IEnumerable)">
            <summary/>
        </member>
        <member name="M:Guard.ArgumentValid(System.String,System.Boolean,System.String)">
            <summary/>
        </member>
        <member name="M:Guard.ArgumentValid(System.String,System.Boolean,System.String,System.Object[])">
            <summary/>
        </member>
    </members>
</doc>
