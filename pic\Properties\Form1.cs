﻿using Nreal_ProductLine_Tool.PBS;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace image_test
{
    public partial class Form1 : Form
    {
        int flag = 0;
        public Form1()
        {
            InitializeComponent();

            JaiController.instance = new JaiController();
            JaiController.instance.callbackShowImage = new JaiController.CallBackShowImage(ShowImage);
            int ret = JaiController.RegisterCallBackShowImage(JaiController.instance.callbackShowImage);

            
        }



        public void ShowImage(IntPtr buff, int width, int height, int type)
        {
            Bitmap bitmap0 = new Bitmap(width, height, width * 3, PixelFormat.Format24bppRgb, buff);
            Bitmap bitmap = new Bitmap(bitmap0.Width, bitmap0.Height, bitmap0.PixelFormat);
            using (Graphics g = Graphics.FromImage(bitmap))
            {
                g.DrawImage(bitmap0, 0, 0);
            }
            this.BeginInvoke
            (
                new MethodInvoker
                (
                    delegate
                    {
                        {
                            switch (type)
                            {
                                case 0:
                                    if(P0.Image != null)
                                        P0.Image.Dispose();                                  
                                    P0.Image = bitmap;
                                    break;
                                case 1:
                                    if (P1.Image != null)
                                        P1.Image.Dispose();
                                    P1.Image = bitmap;
                                    break;
                                case 2:
                                    if (P2.Image != null)
                                        P2.Image.Dispose();
                                    P2.Image = bitmap;
                                    break;
                                case 3:
                                    if (P3.Image != null)
                                        P3.Image.Dispose();
                                    P3.Image = bitmap;
                                    break;
                                case 4:
                                    if (P4.Image != null)
                                        P4.Image.Dispose();
                                    P4.Image = bitmap;
                                    break;
                                case 5:
                                    if (P5.Image != null)
                                        P5.Image.Dispose();
                                    P5.Image = bitmap;
                                    break;
                            }
                        }
                    }
                )
            );
        }

        private void button1_Click(object sender, EventArgs e)
        {
            int type = flag % 5;
            JaiController.TestImgTransfer(type);
            flag++;
        }
    }

    public class JaiController
    {
        public static JaiController instance;

        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void CallBackShowImage(IntPtr ImgData, int Width, int Height, int type);
        public CallBackShowImage callbackShowImage;

        [DllImport("JaiController.dll", EntryPoint = "RegisterCallBackShowImage", CallingConvention = CallingConvention.Cdecl)]
        public static extern int RegisterCallBackShowImage([MarshalAs(UnmanagedType.FunctionPtr)] CallBackShowImage func);
        [DllImport("JaiController.dll", EntryPoint = "TestImgTransfer", CallingConvention = CallingConvention.Cdecl)]
        public static extern int TestImgTransfer(int type);
    }
}
