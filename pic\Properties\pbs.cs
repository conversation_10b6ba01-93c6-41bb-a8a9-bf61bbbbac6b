﻿using ParamManager;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO.Ports;
using System.Linq;
using System.Reflection.Emit;
using System.Runtime.InteropServices;
using System.Runtime.Remoting.Contexts;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using System.Windows.Forms;
using System.Text.Json;
using Label = System.Windows.Forms.Label;
using System.IO;
using System.Reflection;
using System.Web.UI.WebControls.WebParts;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;
using Microsoft.SqlServer.Server;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.ProgressBar;

namespace Nreal_ProductLine_Tool.PBS
{
    public partial class pbs : Form
    {
        [UnmanagedFunctionPointerAttribute(CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        public delegate void ImgTransferCallback(IntPtr data, int count);
        int mesEnable = 0;
        public const int WM_CLOSE = 0x10;
        Form objform;
        Label portDetectLabel;
        string com_val;
        int band_rate;
        bool begin_or_reset = true;
        double previous_val = 0;
        bool isExited = false;
        string snRemote;  // 扫码程序传过来的sn码
        //string snPrism;
        string resMes; // 扫码程序传过来的mes资源
        bool isStopCapture = false;
        protected SemaphoreSlim message_sem;
        ImgTransferCallback imgTransfer = null;
        double[] para = new double[8];
        private Rectangle pictureBoxOriginalBounds;
        private Rectangle groupBoxOriginalBounds;
        private float scaleFactor = 1.0f;
        Dictionary<string, string> mes_resource = new Dictionary<string, string>();
        int trigger = 0;
        int preAllocSize = 60 * 1024 * 1024;
        byte[] managedArray = new byte[60 * 1024 * 1024];

        public pbs()
        {
            int ret = 0;            
            FormBorderStyle = FormBorderStyle.FixedDialog;
            this.FormClosed += FrmMain_FormClosed;

            this.TopLevel = false;
            this.FormBorderStyle = FormBorderStyle.None;
            this.Dock = DockStyle.Fill;

            Logs log = new Logs();
            Logs.WriteInfo("\r\n\r\n", true);
            Logs.WriteInfo("################ start ################", true);
            imgTransfer = imgTransferCallback;

            int[] parameter = new int[16];
                               
            ret = system_init();
            if (ret != 0)
            {
                MessageBox.Show("系统初始化失败");
                Logs.WriteError("系统初始化失败", true);
                //return;
            }
            Logs.WriteInfo("left x:"+ Configure.roi_left_x, true);
            Logs.WriteInfo("right width:" + Configure.roi_right_width, true);
            Logs.WriteInfo("sn_length:" + Configure.sn_length, true);
            
            para_init(ref parameter);
            ret = CameraImageLib.Init_pbs(System.Environment.CurrentDirectory + "\\", parameter);
            Logs.WriteInfo("Init_pbs end", true);

            if (ret != 0)
            {
                //MessageBox.Show("相机初始化失败 " + ret);
                //return;
            }

            if (!Directory.Exists("pic"))
            {
                try
                {
                    Directory.CreateDirectory("pic");
                }
                catch (Exception ex)
                {
                }
            }

            System.Windows.Forms.Control.CheckForIllegalCrossThreadCalls = false;
            this.Load += new EventHandler(MainForm_Load);
            //this.Resize += MainForm_Resize;
            InitializeComponent();
            disableUiFunc();
            this.Text = LOGINFO.rname + " " + LOGINFO.pname + " Version:" + System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString() + " 线号：" + LOGINFO.lname;
            
            //CameraImageLib.SetExposureTime(Configure.camera_exposure_time);
            //CameraImageLib.SetGain(Configure.camera_gain);
            message_sem = new SemaphoreSlim(0, 1);
            Thread thread = new Thread(new ThreadStart(capture_analyze));
            thread.Start();            
        }

        void para_init(ref int[] parameter)
        {  
            parameter[0] = 200;
            parameter[1] = 950;
            parameter[2] = 200;
            parameter[3] = 800;

            parameter[4] = 3500;
            parameter[5] = 750;
            parameter[6] = 200;
            parameter[7] = 800;

            parameter[8] = 1140;
            parameter[9] = 2166;
            parameter[10] = 1000;
            parameter[11] = 200;

            parameter[12] = 1100;
            parameter[13] = 600;
            parameter[14] = 1000;
            parameter[15] = 200;
        }


        int system_init()
        {
            int ret = 0;

            if (LOGINFO.mode == "online" ||
                (LOGINFO.mode == "offline" && LOGINFO.configMode == "net"))
                Configure.Init();
            else
            {
                ret = Configure.Init_Local();
                if (ret != 0)
                {
                    MessageBox.Show("请检查本地配置文件 sys.ini ");
                    Process.GetCurrentProcess().Kill();
                }
            }

            return 0;
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // 设置窗体为全屏  
            this.WindowState = FormWindowState.Maximized;

            pictureBoxOriginalBounds = pictureBox1.Bounds;
            groupBoxOriginalBounds = groupBox1.Bounds;

            groupBox1.Anchor = (AnchorStyles.Top | AnchorStyles.Right);
            groupBox1.Location = new Point(this.ClientSize.Width - groupBox1.Width - 20, 20);

            //pictureBox1.Anchor = (AnchorStyles.Top | AnchorStyles.Left);
            pictureBox1.Location = new Point(20, 20);
            pictureBox1.Size = new Size(this.ClientSize.Width - groupBox1.Width - 60, this.ClientSize.Height - 60);
            //pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            // 初始设置（可根据需要调整）  
            //pictureBox1.SizeMode = PictureBoxSizeMode.Zoom; // 假设你想等比例放大图像  
            //groupBox1.Dock = DockStyle.None; // 确保GroupBox不是停靠的，以便我们可以手动调整其位置和尺寸 
            //pictureBox1.Image = System.Drawing.Image.FromFile("1.bmp");
            // 可选：隐藏任务栏（注意：这可能会影响用户体验）  
            // 调用 API 隐藏任务栏  
            // HideTaskbar();  
        }

        private void FrmMain_FormClosed(object sender, FormClosedEventArgs e)
        {
            Process.GetCurrentProcess().Kill();
        }

        public void imgTransferCallback(IntPtr data, int count)
        {
            if(count > preAllocSize)
                managedArray = new byte[count];
            Marshal.Copy(data, managedArray, 0, count);
            using (MemoryStream stream = new MemoryStream(managedArray))
            {
                System.Drawing.Image bitMap = System.Drawing.Image.FromStream(stream);
                pictureBox1.BeginInvoke((MethodInvoker)delegate
                {
                    // 释放旧的图像资源
                    if (pictureBox1.Image != null)
                    {
                        pictureBox1.Image.Dispose();
                    }
                    pictureBox1.Image = bitMap;
                });
            }
        }

        void test_Execute()
        {
            byte[] snInfo = new byte[1024];
            byte[] resultBuf = new byte[128];
            while (!isExited)
            {
                int len = 1024;
                int resultLen = 128;
                int ret = XrPLCom.SnWaitNotify(snInfo, ref len);
                //this.TopMost = true;
                if (ret != 0)
                {
                    this.TopMost = true;
                    this.WindowState = FormWindowState.Normal;
                    Logs.WriteError("SnWaitNotify error, ret " + ret, true);
                    MessageBox.Show("请重新扫码或联系管理员");
                    this.WindowState = FormWindowState.Minimized;
                    XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
                    continue;
                }

                string jsonString = Encoding.Default.GetString(snInfo, 0, len);
                Logs.WriteInfo("snInfo " + jsonString, true);
                Context ctx = JsonSerializer.Deserialize<Context>(jsonString);
                if (ctx == null)
                {
                    this.TopMost = true;
                    this.WindowState = FormWindowState.Normal;
                    Logs.WriteError("json字符串错误 " + jsonString, true);
                    MessageBox.Show("请重新扫码或联系管理员");
                    this.WindowState = FormWindowState.Minimized;
                    XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
                    continue;
                }
                snRemote = ctx.SN;

                //resMes = ctx.RES;
                if (snRemote.Length != Configure.sn_length)
                {
                    this.TopMost = true;
                    this.WindowState = FormWindowState.Normal;
                    Logs.WriteError("sn码长度错误,实际长度 " + snRemote.Length + " 规格" + Configure.sn_length, true);
                    MessageBox.Show("请输入正确的SN码");
                    this.WindowState = FormWindowState.Minimized;
                    XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
                    continue;
                }
                
                Logs.WriteInfo("db", true);
                isStopCapture = false;
                if (message_sem.CurrentCount == 1)
                {
                    Logs.WriteError("message_sem error", true);
                }
                else
                    message_sem.Release();                
            }
        }

        void capture_analyze()
        {            
            while (!isExited)
            {
                trigger = 0;
                message_sem.Wait();
                //Stopwatch stopwatch = new Stopwatch();

                // 开始计时
                //stopwatch.Start();
                while (!isStopCapture)
                {
                    CameraImageLib.SetExposureTime((double)numericUpDown1.Value);
                    Logs.WriteInfo("begin capture", true);
                    int ret = CameraImageLib.Capture();
                    Logs.WriteInfo("capture end", true);
                    if (ret == 0 && trigger == 1)
                    {
                        ret = CameraImageLib.AnalysisPBS(1, para);
                        Logs.WriteInfo("analyze end", true);
                        this.Invoke
                        (
                            new MethodInvoker
                            (
                                delegate
                                {
                                    {
                                        if (ret != 0)
                                            result_label.ForeColor = Color.Red;
                                        else
                                        {
                                            //result = 1;
                                            //height = para[0];
                                            //width = para[1];
                                            //points = para[2];
                                            result_label.ForeColor = Color.Green;
                                            result_label.Text = "OK";
                                        }
                                        if (ret == -1)
                                            result_label.Text = "未抓到上边沿两条边";
                                        if (ret == -2)
                                            result_label.Text = "上边沿两边间隔过大";
                                        if (ret == -3)
                                            result_label.Text = "上边沿两边间隔过小";
                                        if (ret == -4)
                                            result_label.Text = "上边沿两边间隔过大";
                                        if (ret == -5)
                                            result_label.Text = "上边沿两边间隔过小";
                                        if (ret == -6)
                                            result_label.Text = "左边沿间隔过大";
                                        if (ret == -7)
                                            result_label.Text = "左边沿间隔过小";
                                        if (ret == -8)
                                            result_label.Text = "上边沿两线角度超出范围";

                                        label5.Text = para[0].ToString("F3");
                                        label6.Text = para[1].ToString("F3");
                                        label7.Text = para[2].ToString("F3");

                                        label12.Text = para[4].ToString("F3");
                                        label10.Text = para[6].ToString("F3");
                                        if (para[0] > Configure.upper_line_max_distance
                                            || para[0] < Configure.upper_line_min_distance)
                                        {
                                            label5.BackColor = Color.Red;
                                        }
                                        else
                                            label5.BackColor = Color.Green;

                                        if (para[2] > Configure.upper_line_angle)
                                        {
                                            label6.BackColor = Color.Red;
                                        }
                                        else
                                            label6.BackColor = Color.Green;

                                        /*
                                        if (para[1] > Configure.left_line_max_distance
                                            || para[1] < Configure.left_line_min_distance)
                                        {
                                            label7.BackColor = Color.Red;
                                        }
                                        else
                                            label7.BackColor = Color.Green;
                                        */

                                        if (para[4] > Configure.right_line_max_distance
                                            || para[4] < Configure.right_line_min_distance)
                                        {
                                            label12.BackColor = Color.Red;
                                        }
                                        else
                                            label12.BackColor = Color.Green;

                                        /*
                                        if (para[6] > Configure.bottom_line_max_distance
                                            || para[6] < Configure.bottom_line_min_distance)
                                        {
                                            label10.BackColor = Color.Red;
                                        }
                                        else
                                            label10.BackColor = Color.Green;
                                        */
                                    }

                                }
                            )
                        );
                    }
                    else
                    {
                        result_label.Invoke((EventHandler)delegate
                        {
                            result_label.ForeColor = Color.Red;
                            result_label.Text = "拍照失败";
                        });
                    }

                    //Thread.Sleep(20);
                }
            }
        }

        public int mesOperate()
        {
            byte[] result = new byte[128];
            int len = result.Length;

            int ret = XrPLCom.MesLinkCreate(result, ref len);
            if (ret != 0)
            {
                MessageBox.Show("MES建立连接失败，检查MES服务 ");
                Logs.WriteError("MES建立连接失败，ret " + ret, true);
                return -1;
            }

            string sql = "select LeftResource, RightResource from mes_resource where StationID=\"" + LOGINFO.rname + "\" and StationNo=\"" + LOGINFO.lname + "\"";
            DataSet dataResult = new DataSet();
            VersionHandle.GetDbData(sql, ref dataResult);
            if (dataResult.Tables[0].Rows.Count < 1)
            {
                MessageBox.Show("MES资源不存在 ");
                Logs.WriteError("MES资源不存在 ", true);
                return -2;
            }
            mes_resource.Add("L", dataResult.Tables[0].Rows[0]["LeftResource"].ToString());
            mes_resource.Add("R", dataResult.Tables[0].Rows[0]["RightResource"].ToString());
            Logs.WriteInfo(mes_resource["L"], true);
            Logs.WriteInfo(mes_resource["R"], true);

            len = result.Length;
            ret = XrPLCom.MesLogIn(Encoding.UTF8.GetBytes(mes_resource["L"]), Encoding.UTF8.GetBytes(LOGINFO.user),
                Encoding.UTF8.GetBytes(LOGINFO.pass), result, ref len);
            string message = Encoding.Default.GetString(result, 0, len);
            if (ret != 0 && message.IndexOf("已登录") < 0)
            {
                MessageBox.Show("MES登录失败，失败原因 " + message);
                Logs.WriteError("MES登录失败，失败原因 " + message, true);
                return -3;
            }

            return 0;
        }


        public void enableUiFunc()
        {

        }

        public void disableUiFunc()
        {
        }

        public void errMsgShow(string message)
        {
            this.Invoke
            (
                new MethodInvoker
                (
                    delegate
                    {
                        {
                            MessageBox.Show(message);
                        }

                    }
                )
            );
        }

        public void comWrite(string value)
        {
            com_val = value;
        }

        public void messageShow(string message)
        {

        }

        public void serialStatisticShow(ulong recvBytes, ulong sendBytes, ulong recvPackets, ulong sendPackets)
        {
            this.BeginInvoke
            (
                new MethodInvoker
                (
                    delegate
                    {

                    }
                )
            );

        }

        public void timeValWrite(string val)
        {

        }

        public void productInfoWrite(string ipu, string sensor)
        {
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //.GlueWeight.init();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if(begin_or_reset)
            {
                begin_or_reset = false;
            }
            else
            {
                double cur_val = 0;
                if(cur_val > previous_val || cur_val+1<previous_val)
                {
                    MessageBox.Show("当前胶重与上次胶重对比异常");
                }
                previous_val = cur_val;
            }
                 
        }

        private void button2_Click_1(object sender, EventArgs e)
        {
            begin_or_reset = true;
        }

        private void button1_Click_1(object sender, EventArgs e)
        {
            byte[] resultBuf = new byte[128];
            isStopCapture = true;
            if (LOGINFO.dbWrite == "true")
            {
                string sql = "insert into g_zengliang_bonding(SN," +
                    "Project,Station, Result, user) values " +
                    "(\""
                    + snRemote + "\",\""
                    + LOGINFO.pname + "\",\""
                    + LOGINFO.lname + "\",\"True\",\""
                    + LOGINFO.user + "\")";

                int res = XrPLCom.excuteGinaDb(LOGINFO.mode, LOGINFO.dbWrite, sql);

                if (res != 0)
                {
                    this.TopMost = true;
                    this.WindowState = FormWindowState.Normal;
                    Logs.WriteInfo("excuteDb " + res, true);
                    MessageBox.Show("数据库写入失败");
                    this.WindowState = FormWindowState.Minimized;
                }
            }

            XrPLCom.UploadData(resultBuf, null);
            string fileName = DateTime.Now.ToString("yyyy_M_d_HH_mm_ss") + "_" + snRemote + ".jpg";
            string sPath = System.IO.Path.Combine(Environment.CurrentDirectory, "image/" + DateTime.Now.ToString("yyyy_M_d"));
            //判断文件夹是否存在，如果不存在就创建file文件夹
            if (!Directory.Exists(sPath))
            {
                Directory.CreateDirectory(sPath);
            }
            sPath = sPath + "/";
            pictureBox1.Image.Save(sPath + fileName);
            XrPLCom.UploadData(resultBuf, null);

            dataClear();
        }

        void dataClear()
        {
            result_label.ForeColor = Color.Gray;
            result_label.Text = "";

            label5.Text = "";
            label6.Text = "";
            label7.Text = "";

            label5.BackColor = Color.Gray;
            label6.BackColor = Color.Gray;
            label7.BackColor = Color.Gray;

            para[0] = 0;
            para[1] = 0;
            para[2] = 0;

            //result = 0;
            pictureBox1.Image = null;
        }

        private void label7_Click(object sender, EventArgs e)
        {

        }

        private void button2_Click(object sender, EventArgs e)
        {
            pictureBox1.Image = System.Drawing.Image.FromFile("2.png");
        }

        private void button2_Click_2(object sender, EventArgs e)
        {
            trigger = 1;
        }

        private void button3_Click_1(object sender, EventArgs e)
        {
            CameraImageLib.RegisterCallBackShowImage(imgTransfer);
            TimeSpan start = new TimeSpan(DateTime.Now.Ticks);
            CameraImageLib.AnalysisPBS(1, para);
            TimeSpan end = new TimeSpan(DateTime.Now.Ticks);
            TimeSpan duration = end - start;
            Console.WriteLine($"ana程序执行时间：{duration.TotalMilliseconds} 毫秒");
            Logs.WriteInfo("分析结果:", true);

            label5.Text = para[0].ToString("F3");
            label6.Text = para[1].ToString("F3");
            label7.Text = para[2].ToString("F3");

            label12.Text = para[4].ToString("F3");
            label10.Text = para[6].ToString("F3");
        }
    }
}


