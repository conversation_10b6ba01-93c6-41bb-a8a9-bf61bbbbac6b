﻿using Nreal_ProductLine_Tool.PBS;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.UI.WebControls.WebParts;
using System.Windows.Forms;

namespace image_test
{
    public partial class support_precheck : Form
    {

        [UnmanagedFunctionPointerAttribute(CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        public delegate void ImgTransferCallback(IntPtr data, int count);
        int mesEnable = 0;
        public const int WM_CLOSE = 0x10;
        Form objform;
        Label portDetectLabel;
        string com_val;
        int band_rate;
        bool begin_or_reset = true;
        double previous_val = 0;
        bool isExited = false;
        string snRemote;  // 扫码程序传过来的sn码
        //string snPrism;
        string resMes; // 扫码程序传过来的mes资源
        bool isStopCapture = false;
        protected SemaphoreSlim message_sem;
        ImgTransferCallback imgTransfer = null;

        public support_precheck()
        {
            int ret = 0;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            this.FormClosed += FrmMain_FormClosed;

            this.TopLevel = false;
            this.FormBorderStyle = FormBorderStyle.None;
            this.Dock = DockStyle.Fill;

            Logs log = new Logs();
            Logs.WriteInfo("\r\n\r\n", true);
            Logs.WriteInfo("################ start ################", true);
            imgTransfer = imgTransferCallback;
            InitializeComponent();
            Init_support_precheck();
        }

        private void FrmMain_FormClosed(object sender, FormClosedEventArgs e)
        {
            Process.GetCurrentProcess().Kill();
        }

        public void imgTransferCallback(IntPtr data, int count)
        {
            byte[] managedArray = new byte[count];
            Marshal.Copy(data, managedArray, 0, count);
            MemoryStream stream = new MemoryStream(managedArray);

            Logs.WriteInfo("imgTransferCallback", true);
            System.Drawing.Image bitMap = System.Drawing.Image.FromStream(stream);
            bitMap.Save("11.png");
            pictureBox1.BeginInvoke
            (
                new MethodInvoker
                (
                    delegate
                    {
                        {
                            pictureBox1.Image = bitMap;
                        }
                    }
                )
            );

            //double miliSeconds = ((TimeSpan)(DateTime.Now - begin)).TotalMilliseconds;
            //MessageBox.Show(miliSeconds.ToString());
        }

        private void button1_Click(object sender, EventArgs e)
        {
            CameraImageLib.RegisterCallBackShowImage(imgTransfer);
            TimeSpan start = new TimeSpan(DateTime.Now.Ticks);
            double[] para = new double[11];
            int ret = CameraImageLib.AnalysisSupportPreCheck(1, para);
            TimeSpan end = new TimeSpan(DateTime.Now.Ticks);
            TimeSpan duration = end - start;
            Console.WriteLine($"ana程序执行时间：{duration.TotalMilliseconds} 毫秒");
            Logs.WriteInfo("分析结果:" + ret, true);

            this.Invoke
                (
                    new MethodInvoker
                    (
                        delegate
                        {
                            {
                                if (ret == 0)
                                {
                                    label8.Text = "分析成功";
                                    label8.BackColor = Color.Gray;
                                }
                                else
                                {
                                    label8.Text = "分析失败";
                                    label8.BackColor = Color.Red;
                                }

                                label5.Text = para[0].ToString("F3");
                                label6.Text = para[1].ToString("F3");

                                label7.Text = para[2].ToString("F3");
                                label10.Text = para[3].ToString("F3");

                            }
                        }
                    )
                );
        }

        private void Init_support_precheck()
        {
            /*
            int[] para = new int[16];
            para[0] = 950;
            para[1] = 750;
            para[2] = 200;
            para[3] = 200;
            para[4] = 3808;
            para[5] = 750;
            para[6] = 200;
            para[7] = 200;
            para[8] = 942;
            para[9] = 2102;
            para[10] = 200;
            para[11] = 400;
            para[12] = 3808;
            para[13] = 2332;
            para[14] = 200;
            para[15] = 200;
            */

            int[] para = new int[16];
            para[0] = 492;
            para[1] = 780;
            para[2] = 200;
            para[3] = 200;
            para[4] = 3382;
            para[5] = 780;
            para[6] = 200;
            para[7] = 200;
            para[8] = 475;
            para[9] = 2162;
            para[10] = 200;
            para[11] = 400;
            para[12] = 3388;
            para[13] = 2368;
            para[14] = 200;
            para[15] = 200;

            CameraImageLib.Init_support_preCheck(para, 16);
        }

        private void button3_Click(object sender, EventArgs e)
        {

        }
    }
}
