﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.IO;
using ParamManager;

namespace Nreal_ProductLine_Tool.PBS
{
    internal class Configure
    {
        public List<string> database = new List<string>();

        public static string mes_resource = "NONE";
        public static int sn_length;
        public static int prism_sn_length;

        public static double upper_line_max_distance;
        public static double upper_line_min_distance;
        public static double left_line_max_distance;
        public static double left_line_min_distance;
        public static double right_line_max_distance;
        public static double right_line_min_distance;
        public static double bottom_line_max_distance;
        public static double bottom_line_min_distance;
        public static double upper_line_angle;

        public static int analysis_delay_in_seconds;

        public static int roi_left_x;
        public static int roi_left_y;
        public static int roi_left_width;
        public static int roi_left_height;

        public static int roi_right_x;
        public static int roi_right_y;
        public static int roi_right_width;
        public static int roi_right_height;

        public static int roi_bottom_x;
        public static int roi_bottom_y;
        public static int roi_bottom_width;
        public static int roi_bottom_height;

        public static int roi_bottom_right_x;
        public static int roi_bottom_right_y;
        public static int roi_bottom_right_width;
        public static int roi_bottom_right_height;

        public static int left_thresh;
        public static int right_thresh;

        public static int roi_x;
        public static int roi_y;
        public static int roi_width;

        public Configure()
        {
            //getValueFromConf();
        }

        public static void Init_test(string sysFilePath)
        {
            Logs.WriteDebug("just for test " + sysFilePath, true);
        }

        public static void UpdateStationDataFunc(string stationNo, string projectName, string mode)
        {
            string tt = $" #站号->{stationNo} #项目->{projectName} #模式->{mode}";
        }

        /*
        public static void Init(string sysFilePath)
        {
            flat_debug = Convert.ToBoolean(Configuration.Read("Product", "flat_debug", "false", sysFilePath));
            sn_length = Convert.ToInt32(Configuration.Read("Product", "sn_length", "10", sysFilePath));

            mes_resource = Configuration.Read("Product", "mes_resource", "NONE", sysFilePath);

            max_x = Convert.ToDouble(Configuration.Read("Product", "max_x", "0.25", sysFilePath));
            min_x = Convert.ToDouble(Configuration.Read("Product", "min_x", "-0.25", sysFilePath));
            max_y = Convert.ToDouble(Configuration.Read("Product", "max_y", "0.25", sysFilePath));
            min_y = Convert.ToDouble(Configuration.Read("Product", "min_y", "-0.25", sysFilePath));

            x_transpose = Convert.ToInt32(Configuration.Read("Product", "x_transpose", "1", sysFilePath));
            y_transpose = Convert.ToInt32(Configuration.Read("Product", "y_transpose", "2", sysFilePath));
            d_transpose = Convert.ToInt32(Configuration.Read("Product", "d_transpose", "3", sysFilePath));

            x_parameter = Convert.ToDouble(Configuration.Read("Product", "x_change_rate", "1", sysFilePath));
            y_parameter = Convert.ToDouble(Configuration.Read("Product", "y_change_rate", "1", sysFilePath));
            d_parameter = Convert.ToDouble(Configuration.Read("Product", "d_change_rate", "1", sysFilePath));

            x_offset = Convert.ToDouble(Configuration.Read("Product", "x_offset", "0", sysFilePath));
            y_offset = Convert.ToDouble(Configuration.Read("Product", "y_offset", "0", sysFilePath));

            x_color_red_thresh = Convert.ToDouble(Configuration.Read("Product", "x_color_red_thresh", "0.05", sysFilePath));
            x_color_yellow_thresh = Convert.ToDouble(Configuration.Read("Product", "x_color_yellow_thresh", "0.03", sysFilePath));
            y_color_red_thresh = Convert.ToDouble(Configuration.Read("Product", "y_color_red_thresh", "0.05", sysFilePath));
            y_color_yellow_thresh = Convert.ToDouble(Configuration.Read("Product", "y_color_yellow_thresh", "0.03", sysFilePath));

            auto_upload = Convert.ToBoolean(Configuration.Read("Product", "auto_upload", "true", sysFilePath));
            auto_upload_delay = Convert.ToInt32(Configuration.Read("Product", "auto_upload_delay", "8", sysFilePath));
            opera_min_duration = Convert.ToInt32(Configuration.Read("Product", "opera_min_duration", "10", sysFilePath));
            opera_max_duration = Convert.ToInt32(Configuration.Read("Product", "opera_max_duration", "100", sysFilePath));

            band_rate = Convert.ToInt32(Configuration.Read("HIP", "H410_band_rate", "115200", sysFilePath));
            band_rate = Convert.ToInt32(Configuration.Read("HIP", "H550_band_rate", "38400", sysFilePath));
            product_type = Configuration.Read("HIP", "product_type", "NONE", sysFilePath);
            hipInterTimeVal = Configuration.Read("HIP", "interTimeVal", "1000", sysFilePath);
            int interTimeVal = Convert.ToInt32(hipInterTimeVal);
            if (interTimeVal <= 0 || interTimeVal > 1000)
                hipInterTimeVal = "1000";
            serialRecvTimeOut = Convert.ToDouble(Configuration.Read("HIP", "serialRecvTimeOut", "1000", sysFilePath));
            if (serialRecvTimeOut < 1000)
                serialRecvTimeOut = 1000;
            serialPacketTimeOut = Convert.ToDouble(Configuration.Read("HIP", "serialPacketTimeOut", "60000", sysFilePath));
            if (serialPacketTimeOut < 60000)
                serialPacketTimeOut = 60000;
        }
        */

        public static void Init()
        {
            PM pm;
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;
            pm.InitAll(true, 37, 0);

            Para_get(ref pm);
        }

        public static int Init_Local()
        {
            PM pm;
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;
            int res = pm.ReadConfigFile();
            if (res != 0)
                return res;

            Para_get(ref pm);

            return 0;
        }

        public static void Para_get(ref PM pm)
        {
            sn_length = pm.Vi("Product#sn_length");
            mes_resource = pm.Vs("Product#mes_resource");            

            analysis_delay_in_seconds = pm.Vi("Product#analysis_delay_in_seconds");

            roi_left_x = pm.Vi("Product#roi_left_x");
            roi_left_y = pm.Vi("Product#roi_left_y");
            roi_left_width = pm.Vi("Product#roi_left_width");
            roi_left_height = pm.Vi("Product#roi_left_height");

            roi_right_x = pm.Vi("Product#roi_right_x");
            roi_right_y = pm.Vi("Product#roi_right_y");
            roi_right_width = pm.Vi("Product#roi_right_width");
            roi_right_height = pm.Vi("Product#roi_right_height");

            roi_bottom_x = pm.Vi("Product#roi_bottom_x");
            roi_bottom_y = pm.Vi("Product#roi_bottom_y");
            roi_bottom_width = pm.Vi("Product#roi_bottom_width");
            roi_bottom_height = pm.Vi("Product#roi_bottom_height");

            roi_bottom_right_x = pm.Vi("Product#roi_bottom_right_x");
            roi_bottom_right_y = pm.Vi("Product#roi_bottom_right_y");
            roi_bottom_right_width = pm.Vi("Product#roi_bottom_right_width");
            roi_bottom_right_height = pm.Vi("Product#roi_bottom_right_height");

            upper_line_max_distance = pm.Vd("Verify#upper_line_max_distance");
            upper_line_min_distance = pm.Vd("Verify#upper_line_min_distance");
            left_line_max_distance = pm.Vd("Verify#left_line_max_distance");
            left_line_min_distance = pm.Vd("Verify#left_line_min_distance");
            right_line_max_distance = pm.Vd("Verify#right_line_max_distance");
            right_line_min_distance = pm.Vd("Verify#right_line_min_distance");
            bottom_line_max_distance = pm.Vd("Verify#bottom_line_max_distance");
            bottom_line_min_distance = pm.Vd("Verify#bottom_line_min_distance");
            upper_line_angle = pm.Vd("Verify#upper_line_angle");

            left_thresh = pm.Vi("Product#left_thresh");
            right_thresh = pm.Vi("Product#right_thresh");

            roi_x = pm.Vi("Product#roi_x");
            roi_y = pm.Vi("Product#roi_y");
            roi_width = pm.Vi("Product#roi_width");

        }
    }
}
