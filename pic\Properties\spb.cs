﻿using ParamManager;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO.Ports;
using System.Linq;
using System.Reflection.Emit;
using System.Runtime.InteropServices;
using System.Runtime.Remoting.Contexts;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using System.Windows.Forms;
using System.Text.Json;
using Label = System.Windows.Forms.Label;
using System.IO;
using System.Reflection;
using System.Web.UI.WebControls.WebParts;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;
using System.Web;
using Nreal_ProductLine_Tool.PBS;

namespace Nreal_ProductLine_Tool.PBS
{
    public partial class spb : Form
    {
        [UnmanagedFunctionPointerAttribute(CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        public delegate void ImgTransferCallback(IntPtr data, int count);
        int mesEnable = 0;
        public const int WM_CLOSE = 0x10;
        Form objform;
        Label portDetectLabel;
        string com_val;
        int band_rate;
        bool begin_or_reset = true;
        double previous_val = 0;
        bool isExited = false;
        string snRemote;  // 扫码程序传过来的sn码
        //string snPrism;
        string resMes; // 扫码程序传过来的mes资源
        bool isStopCapture = false;
        protected SemaphoreSlim message_sem;
        ImgTransferCallback imgTransfer = null;
        double[] para = new double[7];
        int[] parameter = new int[16];
        int trigger = 0;
        int show_flag = 1;

        Dictionary<string, string> mes_resource = new Dictionary<string, string>();

        public spb()
        {
            int ret = 0;
            FormBorderStyle = FormBorderStyle.FixedDialog;
            this.FormClosed += FrmMain_FormClosed;

            /*
            ret = CameraImageLib.Init_spb(System.Environment.CurrentDirectory + "\\", 100, 20, 50, 10, 0, 0, 1);
            ret = CameraImageLib.AnalysisSPB(0, para);
            MessageBox.Show("ret " + ret);
            return;
            */

            this.TopLevel = false;
            this.FormBorderStyle = FormBorderStyle.None;
            this.Dock = DockStyle.Fill;

            System.Windows.Forms.Control.CheckForIllegalCrossThreadCalls = false;
            InitializeComponent();
            disableUiFunc();
            this.Text = LOGINFO.rname + " " + LOGINFO.pname + " Version:" + System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString() + " 线号：" + LOGINFO.lname;

            this.Load += new EventHandler(MainForm_Load);

            imgTransfer = imgTransferCallback;
            para_init();
            ret = CameraImageLib.Init_spb(parameter);
            //ret = CameraImageLib.Init_spb(System.Environment.CurrentDirectory + "\\", 700, 250, 1200, 300, 300, 3350, 1000, 300, 300);
            if (ret != 0)
            {
                //MessageBox.Show("相机初始化失败 " + ret);
                //return;
            }

            //CameraImageLib.RegisterCallBackShowImage(imgTransfer);
            //CameraImageLib.SetExposureTime(Configure.camera_exposure_time);
            //CameraImageLib.SetGain(Configure.camera_gain);
            //CameraImageLib.SetExposureTime(Configure.camera_exposure_time);
            //CameraImageLib.SetGain(Configure.camera_gain);

        }


        void para_init()
        {
            parameter[0] = 1200;
            parameter[1] = 450;
            parameter[2] = 1500;
            parameter[3] = 200;

            parameter[4] = 100;
            parameter[5] = 820;
            parameter[6] = 300;
            parameter[7] = 530;

            parameter[8] = 3450;
            parameter[9] = 800;
            parameter[10] = 300;
            parameter[11] = 500;

            parameter[12] = 1000;
            parameter[13] = 1000;
            parameter[14] = 200;
            parameter[15] = 200;

        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // 设置窗体为全屏  
            this.WindowState = FormWindowState.Maximized;

            groupBox1.Anchor = (AnchorStyles.Top | AnchorStyles.Right);
            groupBox1.Location = new Point(this.ClientSize.Width - groupBox1.Width - 20, 20);

            //pictureBox1.Anchor = (AnchorStyles.Top | AnchorStyles.Left);
            pictureBox1.Location = new Point(20, 20);
            pictureBox1.Size = new Size(this.ClientSize.Width - groupBox1.Width - 60, this.ClientSize.Height - 60);
            //pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            // 初始设置（可根据需要调整）  
            //pictureBox1.SizeMode = PictureBoxSizeMode.Zoom; // 假设你想等比例放大图像  
            //groupBox1.Dock = DockStyle.None; // 确保GroupBox不是停靠的，以便我们可以手动调整其位置和尺寸 
            //pictureBox1.Image = System.Drawing.Image.FromFile("1.bmp");
            // 可选：隐藏任务栏（注意：这可能会影响用户体验）  
            // 调用 API 隐藏任务栏  
            // HideTaskbar();  
            //pictureBox1.Image = System.Drawing.Image.FromFile("1.bmp");
        }

        private void FrmMain_FormClosed(object sender, FormClosedEventArgs e)
        {
            Process.GetCurrentProcess().Kill();
        }

        public void imgTransferCallback(IntPtr data, int count)
        {
            byte[] managedArray = new byte[count];
            Marshal.Copy(data, managedArray, 0, count);
            MemoryStream stream = new MemoryStream(managedArray);

            //Logs.WriteInfo("imgTransferCallback", true);
            System.Drawing.Image bitMap = System.Drawing.Image.FromStream(stream);
            bitMap.Save("11.png");
            pictureBox1.BeginInvoke
            (
                new MethodInvoker
                (
                    delegate
                    {
                        {
                            if (pictureBox1.Image != null)
                            {
                                pictureBox1.Image.Dispose();
                            }
                            pictureBox1.Image = bitMap;
                        }
                    }
                )
            );

            //double miliSeconds = ((TimeSpan)(DateTime.Now - begin)).TotalMilliseconds;
            //MessageBox.Show(miliSeconds.ToString());
        }


        public int mesOperate()
        {
            byte[] result = new byte[128];
            int len = result.Length;

            int ret = XrPLCom.MesLinkCreate(result, ref len);
            if (ret != 0)
            {
                MessageBox.Show("MES建立连接失败，检查MES服务 ");
                Logs.WriteError("MES建立连接失败，ret " + ret, true);
                return -1;
            }

            string sql = "select LeftResource, RightResource from mes_resource where StationID=\"" + LOGINFO.rname + "\" and StationNo=\"" + LOGINFO.lname + "\"";
            DataSet dataResult = new DataSet();
            VersionHandle.GetDbData(sql, ref dataResult);
            if (dataResult.Tables[0].Rows.Count < 1)
            {
                MessageBox.Show("MES资源不存在 ");
                Logs.WriteError("MES资源不存在 ", true);
                return -2;
            }
            mes_resource.Add("L", dataResult.Tables[0].Rows[0]["LeftResource"].ToString());
            mes_resource.Add("R", dataResult.Tables[0].Rows[0]["RightResource"].ToString());
            Logs.WriteInfo(mes_resource["L"], true);
            Logs.WriteInfo(mes_resource["R"], true);

            len = result.Length;
            ret = XrPLCom.MesLogIn(Encoding.UTF8.GetBytes(mes_resource["L"]), Encoding.UTF8.GetBytes(LOGINFO.user),
                Encoding.UTF8.GetBytes(LOGINFO.pass), result, ref len);
            string message = Encoding.Default.GetString(result, 0, len);
            if (ret != 0 && message.IndexOf("已登录") < 0)
            {
                MessageBox.Show("MES登录失败，失败原因 " + message);
                Logs.WriteError("MES登录失败，失败原因 " + message, true);
                return -3;
            }

            return 0;
        }


        public void enableUiFunc()
        {

        }

        public void disableUiFunc()
        {
        }

        public void errMsgShow(string message)
        {
            this.Invoke
            (
                new MethodInvoker
                (
                    delegate
                    {
                        {
                            MessageBox.Show(message);
                        }

                    }
                )
            );
        }

        public void comWrite(string value)
        {
            com_val = value;
        }

        public void messageShow(string message)
        {

        }

        public void serialStatisticShow(ulong recvBytes, ulong sendBytes, ulong recvPackets, ulong sendPackets)
        {
            this.BeginInvoke
            (
                new MethodInvoker
                (
                    delegate
                    {

                    }
                )
            );

        }

        public void timeValWrite(string val)
        {

        }

        public void productInfoWrite(string ipu, string sensor)
        {
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //.GlueWeight.init();
        }

        private void button3_Click(object sender, EventArgs e)
        {
            if (begin_or_reset)
            {
                begin_or_reset = false;
            }
            else
            {
                double cur_val = 0;
                if (cur_val > previous_val || cur_val + 1 < previous_val)
                {
                    MessageBox.Show("当前胶重与上次胶重对比异常");
                }
                previous_val = cur_val;
            }

        }

        private void button2_Click_1(object sender, EventArgs e)
        {
            begin_or_reset = true;
        }

        private void button1_Click_1(object sender, EventArgs e)
        {
            byte[] resultBuf = new byte[128];
            isStopCapture = true;
            if (LOGINFO.dbWrite == "true")
            {

                string sql = "insert into g_small_prism_bonding(SN," +
                    "Project,Station, Result, user) values " +
                    "(\""
                    + snRemote + "\",\""
                    + LOGINFO.pname + "\",\""
                    + LOGINFO.lname + "\",\"True\",\""
                    + LOGINFO.user + "\")";

                Logs.WriteInfo("db write " + sql, true);

                int res = XrPLCom.excuteGinaDb(LOGINFO.mode, LOGINFO.dbWrite, sql);

                if (res != 0)
                {
                    this.TopMost = true;
                    this.WindowState = FormWindowState.Normal;
                    Logs.WriteInfo("excuteDb " + res, true);
                    MessageBox.Show("数据库写入失败");
                    this.WindowState = FormWindowState.Minimized;
                }
            }
            Logs.WriteInfo("db write end", true);
            /*
            XrPLCom.UploadData(resultBuf, null);
            string fileName = DateTime.Now.ToString("yyyy_M_d_HH_mm_ss") + "_" + snRemote + ".jpg";
            string sPath = System.IO.Path.Combine(Environment.CurrentDirectory, "image/" + DateTime.Now.ToString("yyyy_M_d"));
            //判断文件夹是否存在，如果不存在就创建file文件夹
            if (!Directory.Exists(sPath))
            {
                Directory.CreateDirectory(sPath);
            }
            sPath = sPath + "/";
            pictureBox1.Image.Save(sPath + fileName);
            */
            XrPLCom.UploadData(resultBuf, null);

            dataClear();
        }

        void dataClear()
        {
            result_label.ForeColor = Color.Gray;
            result_label.Text = "";

            label5.Text = "";
            label6.Text = "";
            label7.Text = "";
            label9.Text = "";

            label5.BackColor = Color.Gray;
            label6.BackColor = Color.Gray;
            label7.BackColor = Color.Gray;
            label9.BackColor = Color.Gray;

            para[0] = 0;
            para[1] = 0;
            para[2] = 0;
            para[3] = 0;

            //result = 0;
            pictureBox1.Image = null;
        }


        private void button4_Click(object sender, EventArgs e)
        {
            int ret = CameraImageLib.AnalysisSPB(show_flag, para);
            this.Invoke
            (
                new MethodInvoker
                (
                    delegate
                    {
                        {
                            if (ret != 0)
                            {
                                result_label.Text = "NG";
                                result_label.ForeColor = Color.Red;

                                label5.Text = "";
                                label6.Text = "";
                                label7.Text = "";
                                //label15.Text = "";
                                label9.Text = "";
                                //label17.Text = "";
                                label13.Text = "";
                            }
                            else
                            {
                                //result = 1;
                                //height = para[0];
                                //width = para[1];
                                //points = para[2];
                                result_label.ForeColor = Color.Green;
                                result_label.Text = "OK";

                                /*
                                if (ret == -1)
                                    result_label.Text = "未抓到上边沿两条边";
                                if (ret == -2)
                                    result_label.Text = "上边沿两边间隔过大";
                                if (ret == -3)
                                    result_label.Text = "上边沿两边间隔过小";
                                if (ret == -4)
                                    result_label.Text = "上边沿角度偏转超出范围";
                                if (ret == -5)
                                    result_label.Text = "左右边未抓全";
                                if (ret == -6)
                                    result_label.Text = "左边沿两边间隔过大";
                                if (ret == -7)
                                    result_label.Text = "左边沿两边间隔过小";
                                if (ret == -8)
                                    result_label.Text = "右边沿两边间隔过大";
                                if (ret == -9)
                                    result_label.Text = "右边沿两边间隔过小";
                                */
                                label5.Text = Math.Abs(para[0]).ToString();
                                label6.Text = Math.Abs(para[1]).ToString();
                                label7.Text = Math.Abs(para[2]).ToString();
                                //label15.Text = Math.Abs(para[3]).ToString();
                                label9.Text = Math.Abs(para[4]).ToString();
                                //label17.Text = Math.Abs(para[5]).ToString();
                                label13.Text = Math.Abs(para[2] - para[4]).ToString();
                                if (para[0] > Configure.upper_line_max_distance
                                    || para[0] < Configure.upper_line_min_distance)
                                {
                                    label5.BackColor = Color.Red;
                                }
                                else
                                    label5.BackColor = Color.Green;

                                if (para[1] > Configure.upper_line_angle
                                    || para[1] < -Configure.upper_line_angle)
                                {
                                    label6.BackColor = Color.Red;
                                }
                                else
                                    label6.BackColor = Color.Green;

                                if (para[2] > Configure.left_line_max_distance
                                    || para[2] < Configure.left_line_min_distance)
                                {
                                    label7.BackColor = Color.Red;
                                }
                                else
                                    label7.BackColor = Color.Green;

                                if (para[4] > Configure.right_line_max_distance
                                    || para[4] < Configure.right_line_min_distance)
                                {
                                    label9.BackColor = Color.Red;
                                }
                                else
                                    label9.BackColor = Color.Green;



                                if (Math.Abs(para[2] - para[4]) > Configure.right_line_max_distance)
                                {
                                    label13.BackColor = Color.Red;
                                }
                                else
                                    label13.BackColor = Color.Green;
                            }

                        }

                    }
                )
            );
        }

        private void button5_Click(object sender, EventArgs e)
        {
            if (button5.Text == "显示辅助线")
            {
                show_flag = 1;
                button5.Text = "取消辅助线";
            }

            else if (button5.Text == "取消辅助线")
            {
                show_flag = 0;
                button5.Text = "显示辅助线";
            }
        }

        private void button4_Click_1(object sender, EventArgs e)
        {
            CameraImageLib.RegisterCallBackShowImage(imgTransfer);
            int ret = CameraImageLib.AnalysisSPB(1, para);
            this.Invoke
            (
                new MethodInvoker
                (
                    delegate
                    {
                        {
                            if (ret != 0)
                            {
                                result_label.Text = "NG";
                                result_label.ForeColor = Color.Red;

                                label5.Text = "";
                                label6.Text = "";
                                label7.Text = "";
                                //label15.Text = "";
                                label9.Text = "";
                                //label17.Text = "";
                                label13.Text = "";
                            }
                            else
                            {
                                //result = 1;
                                //height = para[0];
                                //width = para[1];
                                //points = para[2];
                                result_label.ForeColor = Color.Green;
                                result_label.Text = "OK";

                                /*
                                if (ret == -1)
                                    result_label.Text = "未抓到上边沿两条边";
                                if (ret == -2)
                                    result_label.Text = "上边沿两边间隔过大";
                                if (ret == -3)
                                    result_label.Text = "上边沿两边间隔过小";
                                if (ret == -4)
                                    result_label.Text = "上边沿角度偏转超出范围";
                                if (ret == -5)
                                    result_label.Text = "左右边未抓全";
                                if (ret == -6)
                                    result_label.Text = "左边沿两边间隔过大";
                                if (ret == -7)
                                    result_label.Text = "左边沿两边间隔过小";
                                if (ret == -8)
                                    result_label.Text = "右边沿两边间隔过大";
                                if (ret == -9)
                                    result_label.Text = "右边沿两边间隔过小";
                                */
                                label5.Text = Math.Abs(para[0]).ToString();
                                label6.Text = Math.Abs(para[1]).ToString();
                                label7.Text = Math.Abs(para[2]).ToString();
                                //label15.Text = Math.Abs(para[3]).ToString();
                                label9.Text = Math.Abs(para[4]).ToString();
                                //label17.Text = Math.Abs(para[5]).ToString();
                                label13.Text = Math.Abs(para[2] - para[4]).ToString();
                                if (para[0] > Configure.upper_line_max_distance
                                    || para[0] < Configure.upper_line_min_distance)
                                {
                                    label5.BackColor = Color.Red;
                                }
                                else
                                    label5.BackColor = Color.Green;

                                if (para[1] > Configure.upper_line_angle
                                    || para[1] < -Configure.upper_line_angle)
                                {
                                    label6.BackColor = Color.Red;
                                }
                                else
                                    label6.BackColor = Color.Green;

                                if (para[2] > Configure.left_line_max_distance
                                    || para[2] < Configure.left_line_min_distance)
                                {
                                    label7.BackColor = Color.Red;
                                }
                                else
                                    label7.BackColor = Color.Green;

                                if (para[4] > Configure.right_line_max_distance
                                    || para[4] < Configure.right_line_min_distance)
                                {
                                    label9.BackColor = Color.Red;
                                }
                                else
                                    label9.BackColor = Color.Green;



                                if (Math.Abs(para[2] - para[4]) > Configure.right_line_max_distance)
                                {
                                    label13.BackColor = Color.Red;
                                }
                                else
                                    label13.BackColor = Color.Green;
                            }

                        }

                    }
                )
            );
        }
    }
}



